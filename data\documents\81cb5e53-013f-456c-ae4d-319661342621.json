{"doc_id": "81cb5e53-013f-456c-ae4d-319661342621", "title": "item118_US_Grounding AI Policy  Towards Researcher Access to AI Usage Data", "text": "Grounding AI Policy Towards Researcher Access to AI Usage Data\r\nAI companies should build tools to allow users to donate their usage data to researchers AI companies should develop research APIs that allow users to donate their chat logs and other usage data to vetted, third-party researchers with appropriate safeguards. Among the options for data donation, APIs have the potential to be the easiest for researchers to implement and the most privacy protective for users. A well-designed research API can ensure that participants clearly understand what data they are consenting to share with researchers and limitations on any secondary uses or disclosures and allows them to revoke access at any time. APIs position AI companies as intermediaries, vetting who gets access and for which projects. This allows companies to stop bad actors or privacy violating research projects and impose appropriate privacy and security safeguards (Vogus, 2022). At the same time, companies should not be permitted to censor research because it harms their brand or unduly limit researchers by requiring them to sign onerous data access agreements that grant the company final approval of research before publishing, prevent researchers from sharing the underlying data with peer reviewers with appropriate safeguards, or other undue burdens. Establishing public guidelines for research approval, avoiding nondisclosure agreements for researchers with respect to their findings, and involving neutral third parties to approve researchers and projects can help maintain this balance. Examples such as Twitter’s former academic research API could serve as a good model (Twitter, n.d.). AI transparency reports should include information about how people use their product AI companies should work to develop transparency reports that not only provide information about the safety and governance of their systems, but also give researchers and the public insight into how people use them. AI companies should do this in both a topdown and bottom-up way. They should engage with researchers, policymakers, civil society, and other external stakeholders to determine 26 Recommendations what use case information would be most helpful to them. They should publish this information, and also share their methodologies for analysis so that those relying on these disclosures can better understand how to use them. Companies should also share their own research on how people take advantage of the tools they provide to illuminate unexpected use cases. Finally, companies should share summary information about product usage, both in general and in specific high-risk domains like finance and education, based on input from external stakeholders. AI companies should pilot larger data sharing programs with researchers AI companies should experiment with ways to share usage data directly with researchers in privacy preserving ways. Currently, it is an open question whether existing privacy practices, including those making data access safer (e.g., data clean rooms, indirect company requests) and those making the data reveal less personal or sensitive information (e.g., differential privacy, various de-identification processes), can sufficiently enable companies, rather than users, to share usage data with researchers without compromising privacy. Academic researchers and AI companies should both dedicate resources to developing these methods, and AI companies should be willing to pilot new programs. This could involve allowing researchers to apply for access to specific AI usage data and include methods in their requests to address various privacy and cybersecurity concerns. Social media platforms should include AI information in their research APIs and data portability tools General purpose AI companies are not the only ones with access to data about how people use AI — surfaces where AI-generated data is shared, in particular social media companies, may also have access to this information. Social media companies should include AI-related metadata in their research APIs about data generated both on- and off-platform. For instance, when researchers access public content via the Meta Content Library, that data should include information about whether it was generated using Meta AI or whether an uploaded image included Coalition for Content Provenance and Authenticity (C2PA) or International Press Telecommunications Council (IPTC) metadata that suggests it was AI-generated (C2PA, n.d.; IPTC, n.d.). Platforms can also provide information from other signals they use to determine whether media is CDT ResearchGrounding AI Policy: Towards Researcher Access to AI Usage Data 27 AI-generated (e.g., Google’s SynthID, image scans) and share a confidence interval with researchers. This latter data should only be made available to vetted researchers, since it could potentially be used to reverse-engineer and undermine companies’ AIdetection systems. Finally, platforms that host chatbots on their services, such as Meta and Snapchat, should allow users to export their conversations with these bots as part of their data portability services. Lawmakers should protect users’ abilities to donate their AI usage data to researchers, including through scraping Data donation via user-permitted web scraping can provide meaningful utility to researchers, create a minimal burden for companies, and give users the choice whether to assume any privacy risk from the data sharing. However, in the US, the practice falls in a legal gray area. Policymakers should clarify the law to make sure researchers employing user-permitted web scraping in good faith do not face legal risk from AI companies, and that AI companies are not held liable for mishandling user data if they allow such tools. There is precedent for companies using privacy law as a justification for shutting down independent research in social media. Facebook, for instance, shut down the NYU Ad Observer, a browser extension that users could install to scrape and donate data on the political ads they encountered in their feed, arguing that it went against their Terms of Service and their FTC consent order (Clark, 2021). Similarly, Facebook shut down AlgorithmWatch’s project to allow users to donate information about what they were recommended on their Instagram newsfeed, also using scraping, arguing that it went against their Terms of Service and GDPR (Kasyer-Bril, 2021). Both projects had explicit user consent, were open source, and were instrumental to journalism covering Facebook. The NYU Ad Observer was also independently audited for user privacy and security (Mozilla, n.d.). In response, the FTC clarified that the NY Ad Observer did not go against the consent order, and reprimanded Facebook for using it “as a pretext to advance other aims” (Levine, 2021). US lawmakers also proposed a number of laws shortly after this incident that protected researcher’s abilities to independently scrape and analyze platforms, including the Platform Accountability and Transparency Act (PATA), the Social Media Data Act, and the Digital Services Oversight and Safety Act. The EU went one step further, protecting researcher access to data under DSA Article 40.28 Recommendations Lawmakers should offer similar protections to researchers and AI companies concerning users donating their usage data. By issuing clarification that AI companies will not be accused of privacy violations for supporting good faith, privacy-protective research efforts, companies cannot hide behind the figleaf of legal liability when prohibiting research, and companies that are actually interested in sharing this data could do so without fear of recourse. This can be done through bills explicitly designed to protect researcher access to data, or through government and multilateral bodies offering guidance on best practices. Lawmakers should encourage transparency reports that include information about how people use AI systems in high-risk domains As previously discussed, global voluntary commitments for transparency have primarily focused on AI companies disclosing their models’ limitations, capabilities, and safety efforts, including benchmarking and red-teaming. Lawmakers should expand these to include information about how people use AI applications. In particular, lawmakers should push AI companies to work with experts in high risk domains where their products may be used — such as finance, healthcare, and education — to understand what information they could provide to help people more safely use their products in these ways. They can do this by providing guidelines and best practices for involving external stakeholders in the design of transparency reports. Companies should publish this information alongside their methodologies for analysis, so that those relying on these disclosures can better understand how to use them.", "metadata": {"original_filename": "item118_US_Grounding AI Policy  Towards Researcher Access to AI Usage Data.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:19.973955", "updated_at": "2025-08-29T02:04:19.973955", "word_count": 8906}