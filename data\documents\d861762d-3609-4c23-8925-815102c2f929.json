{"doc_id": "d861762d-3609-4c23-8925-815102c2f929", "title": "item273_US_RICHARD A. <PERSON>NK Of Counsel American Civil Liberties Union of Idaho Foundation", "text": "Testimony of <PERSON> Chair <PERSON>, Ranking Member <PERSON>, and members of the committee: thank you for your attention to artificial intelligence and automated decisionmaking in government programs, and for inviting me to testify about it at this important hearing. I was invited here today because I have been working for over a decade with Idahoans with developmental disabilities and their families to address black boxes around automated decision-making hidden in a federally funded program. Only through the litigation that I’ve helped these families pursue were they able to access the secret computerized algorithms the State of Idaho uses to make decisions about the health care they depend on day to day. Once we opened the black box that concealed that automated system, we found that Idaho built the system out of corrupt data, relied on inputs that the State never validated, and produced unfair results that even those who created it could not explain. A federal court ruled that the system was unconstitutional. Yet a decade after filing suit and over seven years since winning in court we are still litigating the case, battling for due process against still more black box secrecy. The lesson is this: Decades-long class actions by indigent families are not a viable plan for AI governance in federal programs. We need federal regulation and enforcement to protect basic fairness and constitutional rights and establish clear guardrails for whether and how government programs use AI and other automated decision-making systems. I am Of Counsel with the American Civil Liberties Union of Idaho, where I was previously the Legal Director for nine years. Before that I held the title of Justice Architect at Idaho Legal Aid Services, a federally funded civil legal services program where the case I’m here to testify about today originated. I have also held a Fulbright Fellowship, during which I studied strategies for community education about legal rights and responsibilities. My principal practice currently is as an attorney and co-founder of Wrest Collective, a community-funded, sliding scale nonprofit law firm in Boise, Idaho. 1The K.W. v. Armstrong Lawsuit The State of Idaho, through its federally funded Medicaid program, provides health care for some of its residents experiencing drastic poverty. Among those eligible for Medicaid in Idaho are certain people with developmental and intellectual disabilities. Although in the past the government would have confined these people in state hospitals and other institutional facilities, through Medicaid they can choose to get services at home and in their communities instead—at a savings to the government and taxpayers. Those who are eligible must present themselves annually for an assessment. In Idaho’s system, assessors complete two assessment instruments for each person. The assessor plugs these assessment results into an automated system that converts them into a dollar amount for each person. That amount is called the person’s “budget.” It’s the presumptive maximum amount the person can use for the Medicaid services related to their developmental or intellectual disabilities over the coming year. Medicaid does not pay the person their budget amount. Rather, the person’s support providers get reimbursed from the budget for services actually rendered. The lawsuit I’m here to testify about, known as K.W. v. Armstrong, began in 2012. People across Idaho started getting notices, after their annual assessments, that their automated budgets had dropped dramatically. Some of them saw their individualized budgets suddenly drop by tens of thousands of dollars and more than 30%. For one of my clients, the late Christie Mathwig, the automated system suddenly cut her budget by 21% with no explanation. Living in rural Idaho where wildfires and winter storms are common, she could not evacuate in an emergency or administer her own medications without help. But under her reduced budget, she would lose the 24-hour assistance she needed to survive. Another client, who I’ll call by her initials “A.L.” as used in the lawsuit, was institutionalized when she was 9 years old. She remained in institutions and hospitals and incarcerated until she had access to Medicaid services for a community placement in Idaho. Though she Christie Mathwig in her rural Idaho home 2previously had access to a $42 thousand budget to pay for Medicaid services she needed, in 2012 Idaho’s automated system cut her budget by nearly 20% to $34 thousand with no explanation. Another of my clients, “Matthew S.,” a then 40 year-old man living in his community with the help of developmental therapy, saw his budget drop from $52 thousand to $34 thousand, a 35% cut. Even the State’s own assessor noted that a reduction in his services would result in reduced independence and loss of skills. But, as with Christie and A.L., the State gave no explanation for the cut. For each of them, these sudden and severe cuts put their independence, their safety, and their liberty at immediate risk. Christie, A.L., Matthew, and others reached out to me for legal help. I naively sent a letter asking the State to explain how it translated assessment results into individual budget amounts. The State’s Department of Health and Welfare, which administers the Medicaid program in Idaho, refused to explain it. Its attorneys replied that the system was a “trade secret.” After that response, I helped thirteen people with severe budget cuts file a joint lawsuit. We asked a federal court to order the Department to disclose its system. Within a few weeks of filing suit, we got that order. Then we got the system. It was a set of formulas in a fairly basic Microsoft Excel spreadsheet. The Department’s assessors enter annual assessment results into a copy of the spreadsheet for each person. The spreadsheet, in hidden cells, computes the person’s budget amount. Now that we had the formulas, we started trying to make sense of them. We got what little documentation there was about them. We learned that the Department concocted the formulas in-house. It also created one of the two assessment instruments that feed the inputs to those formulas. Department staff had just brainstormed the assessment questions. They never validated, standardized, or audited the instrument. We took the testimony of the state agency employee who devised the formulas and learned that he used statistical predictions to select them. Specifically, he used statistical software to predict how much Medicaid users would spend in the future, extrapolating based on their assessment results and how much they had spent in the past. We also took testimony from the head of the Department’s developmental disabilities bureau, the Department worker who supervised review of individuals’ Medicaid service plans, the assessment supervisor, and others. They acknowledged that my clients’ budgets dropped suddenly after the Department re-ran the statistical modeling and revised its formulas. But none of them could explain why the new statistical model cut some individual budgets so severely. Each time we asked who could explain it, the witness deposed that day would point the finger towards another Department 3bureaucrat. By the end of the depositions, the finger pointing had gone around in circles. We had to hire three experts to make sense of the State’s formulas, the data the Department based them on, the statistical modeling it derived them from, and the impact they had on our clients. The experts found serious problems with both the data and the modeling, and therefore the formulas themselves. When we presented our evidence to the court, the court agreed. The court ruled that the Department’s formulas were so unreliable that they arbitrarily deprived people of their Medicaid budgets and violated the due process guaranteed in the United States Constitution. To reach its ruling, the court found that the data the Department fed into the statistical modeling was incomplete and probably full of errors to start with. The Department statistician who developed the system, the court noted, later discovered the data harbored geographic bias as well. The resulting formulas would never compute an adequate budget for about 15% of people, the court concluded; and when human Department workers reviewed the automated budgets, they increased more than 60% of them. But the Department did not explain why a person’s budget went down, partly because nobody even within the Department could meaningfully understand the automated cuts. The agency also had no written criteria for increasing budgets after human review. Many who contested their budget cuts would need help from a skilled advocate to handle the complex appeal process, anyhow. In short, the automated system was unconstitutional. And the human review available to correct bad automated decisions was not enough to save it. Idaho’s system, rudimentary compared to some of the complex AI available today, highlights how critical mistakes in statistical models can rapidly accumulate even in what seem on the surface to be simple automations. The problems did not end there, either. Though one of the two assessment instruments that Idaho used was its own unvalidated tool, the other was a standardized instrument developed by a private company. The company, with the State’s help, fought to ban people with developmental disabilities from accessing the assessment booklet that directs assessors in completing the tool and details each person’s individual scores. But, as the court had to point out, without that booklet, people relying on these Medicaid services could not effectively crossexamine the assessors or challenge errors in their automated budgets. The court ordered the Department to overhaul its automated system. The court also ordered the Department to ensure access to all parts of the assessment booklet needed to fully challenge a budget cut, regularly test the new system, set out criteria for what a person has to show to get their automated budget increased, and make certain that everyone in the Medicaid program has a 4“suitable representative” to help pursue a higher budget through the Department’s appeal process.1 Just to reach that point in the case, we had to spend over $40 thousand on experts to analyze all the problems with the automated system. Plus, our team had to put in more than 2,000 attorney and paralegal hours to vindicate our clients’ constitutional rights and secure a settlement agreement after the court’s ruling. Unfortunately, the case continues today as we contend against a proposed new system that repeats some of the same problems the old system had. After the court approved a settlement agreement, the Department selected a new assessment instrument on which it began building a new automated budget system. Despite the court’s prior rulings—and a subsequent meeting where stakeholders made clear that transparency was essential for the new instrument —we learned that the Department had chosen another black box assessment instrument. As it did before, the State helped this instrument’s private publisher f ight to ban people with developmental disabilities and their advocates from accessing the manual that assessors must follow to properly complete the assessment. Once again, the publisher claimed this vital information was proprietary. But without the manual, people relying on these Medicaid services cannot effectively cross-examine assessors or challenge errors in the automated budgets the State would assign to them. We argued over this secrecy in court again this spring. But following the argument and before the court could issue a decision, the private publisher told the State it would not let its instrument be used in Idaho if indigent Medicaid recipients might get to access the manual, which it sells for $130 on its website. Transparency may not be the only problem with the new system, either. Preliminary analysis of the new system—including by the firm that helped the State develop it—suggests that the new system is biased against some groups of people who use this Medicaid program. The pre-implementation data showed, for example, that the system could produce budgets that would only “somewhat” meet the needs of 23% of people in one group and “not at all” meet the needs of another 23% in that group. Although this long-running litigation has kept these recurring problems with automated government decision-making systems in check, it should not require a class action lawsuit to ensure that these systems meet constitutional minimums. Federal policies, regulations, and enforcement are appropriate and essential to govern automated decision-making systems like these, and to safeguard against the life-upending harm they can cause when not implemented reliably and fairly. Dangers this Lawsuit Flags With governments increasingly relying on automated decision-making systems, scholars, advocates, judges, and policymakers alike have begun to realize these systems’ sweeping effects and complex risks. The K.W. v. Armstrong case is among the most instructive litigation challenges to one of these systems. The White House’s October 2022 Blueprint for an AI Bill of Rights references this litigation,2 and the case continues to be featured in leading scholarship on civil and human rights in this context.3 It is probably so often cited because it 2 White House Office of Science and Technology Policy, Blueprint for an AI Bill of Rights: Making Automated Systems Work for the American People 42 & n.86 (Oct. 2022), https://www.whitehouse.gov/wp-content/uploads/2022/10/Blueprint-for-an-AIBill-of-Rights.pdf. 3See, e.g., Rashida Richardson, Defining and Demystifying Automated Decision Systems, 81 MD. L. REV. 785, 800 & n.70 (2022); Sarah Brown, Promulgating Poverty: How AI Technology Exacerbates Poverty Issues in Public Programs, 49 N. KY. L. REV. 267, 277–281 (2022); Chris Chambers Goodman, AI, Can You Hear Me? Promoting Procedural Due Process in Government Use of Artificial Intelligence Technologies, 28 RICH. J.L. & TECH. 700, 718 n.81, 720 & nn. 90–91 (2022); Francesca Bignami, Artificial Intelligence Accountability of Public Administration, 70 AM. J. COMP. L. 312, 333 n.65 (2022); Ryan Calo & Danielle Keats Citron, The Automated Administrative State: A Crisis of Legitimacy, 70 EMORY L.J. 797, 823 (2021); Kristen E. Egger, Artificial Intelligence in the Workplace: Exploring Liability Under the Americans with Disabilities Act and Regulatory Solutions, 60 WASHBURN L.J. 527, 542 (2021); Cary Coglianese & Lavi M. Ben Dor, AI in Adjudication and Administration, 86 BROOK. L. REV. 791, 834 (2021); Hannah Bloch-Wehba, Access to Algorithms, 88 FORDHAM L. REV. 1265, 1279 (2020); Sarah Valentine, Impoverished Algorithms: Misguided Governments, Flawed Technologies, and Social Control, 46 FORDHAM URB. L.J. 364, 414 (2019); see also Sarah Brown, Promulgating Poverty: How AI Technology Exacerbates Poverty Issues in Public Programs, 49 N. KY. L. REV. 267, 277 (2022); Charles Tait Graves & Sonia K. Katyal, From Trade Secrecy to Seclusion, 109 GEO. L.J. 1337, 1379 nn.207–208 (2021); Noah Bunnell, Remedying Public-Sector Algorithmic Harms: The Case for Local and State Regulation Via Independent Agency, 54 COLUM. J.L. & SOC. PROBS. 261, 303 n.83 (2021); Christopher Slobogin, Preventive Justice: How Algorithms, Parole Boards, and Limiting Retributivism Could End Mass Incarceration, 56 WAKE FOREST L. REV. 97, 166 n.357 (2021); Frank Pasquale, Normative Dimensions of Consensual 6illustrates several of the many ways that automated decision-making systems can go wrong. These illustrations are especially poignant considering how rudimentary Idaho’s system was: if formulas on a basic Excel spreadsheet can present so many constitutional problems, governance to safeguard against these dangers in today’s more complex AI systems are all the more critical. We discovered, and had to litigate, each of the following failures of automated decision-making as part of this case:  Black boxes concealing that the government is using an automated system Although Idaho had been using an automated budgeting system for several years before the K.W. v. Armstrong lawsuit began, until then the State did not tell Medicaid recipients anything about it, including that it was using an automated system at all. Only after I sent a letter asking the state Medicaid agency to explain how it computed the sudden 2012 budget reductions did my clients learn that the State considered the system a “trade secret”—and only after we filed a federal lawsuit did we learn that secret was a handful of formulas coded into an Excel spreadsheet. It was a lucky coincidence both that my clients connected with me when I had time, as a busy legal aid lawyer, to take on their case, and that I was naive enough to demand to know how the State had come up with my clients’ budget amounts. Undoubtedly, other states, federal programs, and maybe even other programs in Idaho are currently using AI or automated systems that remain undisclosed to the people those systems make decisions about. This raises core due process concerns. People are unaware that automated systems are being used, let alone how they work or that the systems may be making erroneous or discriminatory decisions. This severely curtails their ability to seek legal redress to protect their rights.  Black boxes concealing flaws in the automated system Once a federal court order got Idaho’s secret formulas into our hands, we had to figure out how they worked and how Idaho had developed them. We learned, through this process, that the state agency’s cursory internal documentation of its system could not sufficiently explain it. It took months of litigation discovery to gather the evidence necessary to evaluate the system, f igure out how it worked, and identify the assumptions and data it relied on. Agency officials pointed the finger at each other when asked to explain the system’s basic functions, and ultimately none could meaningfully explain it at all. We had to spend over $40 thousand on a statistician, a Medicaid resource allocation specialist, and a developmental disability expert to reverse engineer the system, catalog its flaws, and assess the harm its results could wreak upon our clients. Notably, the lack of transparency compounded this system’s harms. Not only did Idaho create and deploy a tool that made erroneous determinations, but it did so without understanding how the tool worked and without adequate processes to detect the errors.  Corrupt and discriminatory data As our experts evaluated the automated system after we got our hands on it, they discovered that it was built from corrupt and biased data. The inputs that drove the system’s formulas, for starters, came partly from a homegrown assessment instrument that the State had never validated or standardized. And the State had done nothing to ensure consistency across different assessors or with the same assessor over time. Then, out of the data the State compiled to compute the statistical formulas at the heart of its system, more than two thirds of the records were either plainly erroneous, mismatched with the agency’s systems, or contained incomplete or unbelievable information. Further analysis then revealed that data from one of Idaho’s most populous regions was underrepresented in the data sample the Department used. This oversight made a substantial difference, unjustifiably biasing the system’s operation. 8 Black boxes preventing accountability Compounding all these problems, Idaho has also repeatedly fought to withhold from Medicaid recipients the very information they need to challenge the automated system’s results. During the initial round of litigation in this case, the State tried to ban my clients from using the assessment booklet to challenge their budgets. The State lost in court, with the judge ruling that such a ban would violate constitutional due process guarantees. Automated systems must preserve the rights of those they make decisions about to challenge errors and cross-examine assessors and others whose determinations impact the system’s results. This spring, yet again, the State fought to withhold critical information about the new assessment it planned to implement. Taking up the cause of its private contractor, rather than the constitutional rights of Idahoans relying on Medicaid, the State tried to ban my clients from accessing the manual that sets out what assessors must do for their assessments to be valid. While we awaited a court ruling on such a ban, the private contractor pulled out of its relationship with the State. When governments spend taxpayer funds on contractors who claim proprietary protections in public programs that make decisions about individual lives, private interests can hold constitutional rights hostage. Federal policy and federal agencies supervising federal programs like Medicaid, and not just federal courts, should prohibit this. As another court put it in a similar case: “When a public agency adopts a policy of making high stakes . . . decisions based on secret algorithms incompatible with minimum due process, the proper remedy is to overturn the policy, while leaving the trade secrets intact.”4 Solutions Since the K.W. v. Armstrong lawsuit was filed in 2012, there has been an explosion of attention on AI and automated decision-making by policymakers, scholars, and scientists. At each stage of the case, we’ve had more and better resources to turn to as we try to remedy the problems with Idaho’s system. The lawsuit has served as a laboratory as the court and the parties have explored the complexities of automated decision-making. Some of the most salient and important solutions these lessons point to fall into three categories: The people who AI and automated systems make decisions about must be integrally involved with the development, implementation, and assessment of those systems This solution is the most important, because if earnestly implemented it will be the most effective at preventing the dangers AI poses. In my own work for over a decade litigating automated decision-making in Idaho’s Medicaid system, I have failed again and again to spot systemic problems that my clients experience as everyday realities. Only by cultivating long-term dialogue with people using Medicaid services across Idaho have I discovered some of the system’s many veiled flaws. Bureaucrats, advocates, courts, computer scientists, and other technical experts do not have the experience necessary to assess automated decision-making systems and understand their impacts at the depth that those who these systems make decisions about can. A cornerstone of the court-ordered settlement agreement in the K.W. v. Armstrong case addressed this, requiring the Idaho state agency to “encourage engagement and active involvement of class members, their guardians, and other community stakeholders” as it developed a new system, and to do so “throughout the development of the new [system].”5 To ensure that involvement would be meaningful, the settlement agreement also requires that communications engaging with my clients about the new system’s development must “use clear language and layout, appropriate to the circumstances of the class members and their guardians.”6 Policies and regulations governing uses of AI and automated decisionmaking in government programs should include the same requirements; and appropriate agencies should enforce those requirements. This is consistent with the very first principal of the Blueprint for an AI Bill of Rights, which begins by instructing that “[a]utomated systems should be developed with consultation from diverse communities, stakeholders, and domain experts to identify concerns, risks, and potential impacts of the system.”7   Federal and state agencies must protect constitutional rights to due process, transparency, and equal protection through regulation and enforcement specific to AI and automated systems For public programs that individuals and families depend on for their health and safety, like Medicaid, their constitutional and statutory rights to due process, transparency, and equal protection are, largely, already well established. But, as the K.W. v. Armstrong litigation continues to demonstrate, those rights must be carefully guarded, and courts are not best suited to make sure that governments respect civil and constitutional rights when they implement automated systems. Litigation is immensely expensive and time-intensive: this one lawsuit about one automated system in one bureau within the Medicaid program in one state with a population of just 2 million people is still going after 11 years and hundreds of thousands of dollars in costs and attorney fees. Caseby-case litigation over constitutional rights in programs like this will never meet the rising tide of these AI systems, nor can it fully undo the harms that the application of these systems can cause to impacted individuals and communities. Rather, agencies, such as the Centers for Medicare and Medicaid Services that administers the federal Medicaid program, must prescribe regulations to govern the use, implementation, and testing of automated systems used within particular programs—and must also closely monitor and enforce compliance with those regulations.  These regulations should address not only the newest, most cutting-edge versions of automated-decision systems such as generative AI but must likewise address the simpler but no less impactful algorithms and automated systems that are already used to make critical decisions throughout the government.8 As our litigation demonstrates, agencies should play a driving role. Agencies must set standards for assessing whether and how automated decisionmaking  should be used at all, along with standards for independent pre- and post-deployment audits that center civil rights and civil liberties as well as safety and effectiveness. Agencies must also erect and enforce guardrails for determining when mitigation or decommission measures are appropriate. Congress and federal agencies are especially well positioned as stewards of federal tax dollars to lead in implementing such standards in federally funded programs like Medicaid. In addition to assessments and audits, programs considering or already using automated systems must implement proper notice for the people against whom these systems are deployed, plus robust opt-out and human review processes. Especially in programs like the one in K.W. v. Armstrong, which my clients rely on for their day-to-day safety and survival, the risk of harm from erroneous decisions is extraordinary. Plus, my clients’ disabilities and indigency make conventional administrative appeal processes inaccessible and often impossible for them to use. When using automated systems, government agencies must make it easy and accessible for people to “raise their hand” to opt out or have an informed human review automated results to check their validity. Those who these systems make decisions about should also always have private rights of action so they can access judicial review if agency-level protections fail.  Government AI and automated systems must be fully transparent and subject to clear standards from before they start until after they finish Black boxes have plagued the Idaho system challenged in the K.W. v. Armstrong case from its filing in 2012 through to the present, in 2023. The system has been shrouded in black boxes around its use and implementation, its functioning, and critical information to challenge its validity and results. The state agency’s involvement with private contractors claiming proprietary interests in decision-making methods has exacerbated these black box problems. Most recently, one of those contractors has refused to cooperate with the agency rather than risk the transparency required by constitutional due process. Beginning when government programs first consider using a new automated system, and then throughout each system’s design and implementation, as well as during testing and post-use evaluation, government AI and automated systems must be fully transparent. Those who these systems make decisions about have due process rights to challenge those decisions and equal protection rights against bias.  They and the public must always have access to the same information that the government does about whether automated systems are in use, how these systems work, the data they were trained on, any algorithms and instructions they follow, and all testing and analysis results. Moreover, it is critical that the auditing regime include external audits and community review.  Private interests in trade secrets or proprietary methods and materials can never be allowed to trump individual due process rights, and taxpayers must retain robust access to oversee these public systems.", "metadata": {"original_filename": "item273_US_RICHARD A. <PERSON>NK Of Counsel American Civil Liberties Union of Idaho Foundation.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:21.229450", "updated_at": "2025-08-29T02:04:21.229450", "word_count": 29040}