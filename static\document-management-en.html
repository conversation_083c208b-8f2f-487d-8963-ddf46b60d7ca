<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management - Intelligent Document Analysis System</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <style>
        .document-management {
            padding: 15px;
        }

        .management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: var(--border-radius);
        }

        .search-filters {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 12px;
            margin-bottom: 15px;
            padding: 15px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 8px 35px 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 13px;
            transition: var(--transition);
        }

        .search-box input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 14px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .filter-group label {
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .filter-group select {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 13px;
            background: white;
        }
        
        .document-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 3px;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 12px;
        }
        
        .document-list {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        .list-header {
            display: grid;
            grid-template-columns: 30px 2fr 100px 80px 80px 100px;
            gap: 10px;
            padding: 8px 15px;
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-secondary);
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
        }

        .document-item {
            display: grid;
            grid-template-columns: 30px 2fr 100px 80px 80px 100px;
            gap: 10px;
            padding: 8px 15px;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            align-items: center;
            font-size: 13px;
        }

        .document-item:hover {
            background: var(--bg-secondary);
        }

        .document-item:last-child {
            border-bottom: none;
        }

        .document-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .document-title {
            font-weight: 500;
            color: var(--text-primary);
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .document-title:hover {
            color: var(--primary-color);
        }

        .document-meta {
            font-size: 11px;
            color: var(--text-muted);
            margin-top: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .document-date {
            color: var(--text-muted);
            font-size: 12px;
        }

        .document-words {
            color: var(--text-muted);
            font-size: 12px;
        }

        .document-type {
            display: inline-block;
            padding: 2px 6px;
            background: var(--primary-color);
            color: white;
            border-radius: 8px;
            font-size: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }
        
        .document-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: var(--transition);
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-view {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .action-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 20px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .pagination button:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .pagination .current-page {
            background: var(--primary-color);
            color: white;
        }
        
        .batch-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            padding: 10px 15px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            align-items: center;
        }

        .batch-actions button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: var(--transition);
        }
        
        .btn-batch-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-batch-export {
            background: var(--success-color);
            color: white;
        }
        
        .selected-count {
            margin-left: auto;
            color: var(--text-muted);
            font-size: 14px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--text-muted);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: var(--text-secondary);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: var(--border-radius);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-muted);
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .modal-footer button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: var(--transition);
        }
        
        .btn-cancel {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
        }
        
        .btn-confirm {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        /* 文档预览模态框样式 */
        .preview-modal-content {
            max-width: 800px;
            max-height: 90vh;
        }

        .preview-modal-body {
            max-height: 60vh;
            overflow-y: auto;
        }

        .preview-metadata {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .metadata-item {
            font-size: 14px;
            line-height: 1.4;
        }

        .metadata-item strong {
            color: var(--text-primary);
        }

        .preview-content-wrapper h4 {
            margin: 0 0 10px 0;
            color: var(--text-primary);
            font-size: 16px;
        }

        .preview-content {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            border: 1px solid var(--border-color);
        }

        /* 暗色主题下的预览样式 */
        [data-theme="dark"] .preview-content {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .preview-metadata {
            background: var(--bg-tertiary);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1 data-i18n="titles.systemName">📁 Document Management System</h1>
            <p class="subtitle" data-i18n="titles.subtitle">Intelligent Document Management and Analysis Platform</p>
            <nav class="main-nav">
                <a href="/" class="nav-link" data-i18n="nav.home">🏠 Document Analysis</a>
                <a href="/static/visualization-en.html" class="nav-link" data-i18n="nav.visualization">📊 Data Visualization</a>
                <a href="/static/data-table-en.html" class="nav-link" data-i18n="nav.tableView">📋 Table View</a>
                <a href="/static/document-management-en.html" class="nav-link active" data-i18n="nav.documentManagement">📁 Document Management</a>
                <a href="/static/prompts-en.html" class="nav-link" data-i18n="nav.promptEditor">🔧 Prompt Editor</a>
                <a href="/docs" class="nav-link" data-i18n="nav.apiDocs">📚 API Documentation</a>
                <button class="language-toggle" onclick="toggleLanguage()" title="Switch Language">中</button>
                <button type="button" class="theme-toggle" onclick="toggleTheme()" data-i18n="analysis.switchTheme" title="Switch Theme">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main class="document-management">
            <!-- 管理头部 -->
            <div class="management-header">
                <div>
                    <h2 data-i18n="documentManagement.managementTitle">📁 Document Management</h2>
                    <p data-i18n="documentManagement.managementDesc">Manage all your documents with search, filter, and batch operations</p>
                </div>
                <button class="btn-primary" onclick="refreshDocuments()" data-i18n="documentManagement.refresh">🔄 Refresh</button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-filters">
                <div class="search-box">
                    <input type="text" id="search-input" data-i18n="documentManagement.searchPlaceholder" placeholder="Search document title or content...">
                    <button class="search-btn" onclick="searchDocuments()">🔍</button>
                </div>
                <div class="filter-group">
                    <label data-i18n="documentManagement.sortBy">Sort By</label>
                    <select id="sort-select" onchange="applySorting()">
                        <option value="created_at_desc" data-i18n="documentManagement.sortCreatedDesc">Created Time (Newest First)</option>
                        <option value="created_at_asc" data-i18n="documentManagement.sortCreatedAsc">Created Time (Oldest First)</option>
                        <option value="title_asc" data-i18n="documentManagement.sortTitleAsc">Title (A-Z)</option>
                        <option value="title_desc" data-i18n="documentManagement.sortTitleDesc">Title (Z-A)</option>
                        <option value="word_count_desc" data-i18n="documentManagement.sortWordsDesc">Word Count (High to Low)</option>
                        <option value="word_count_asc" data-i18n="documentManagement.sortWordsAsc">Word Count (Low to High)</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label data-i18n="documentManagement.docType">Document Type</label>
                    <select id="type-filter" onchange="applyFilters()">
                        <option value="" data-i18n="documentManagement.allTypes">All Types</option>
                        <option value="政策文件" data-i18n="documentManagement.policyDoc">Policy Document</option>
                        <option value="研究报告" data-i18n="documentManagement.researchReport">Research Report</option>
                        <option value="新闻报道" data-i18n="documentManagement.newsReport">News Report</option>
                        <option value="学术论文" data-i18n="documentManagement.academicPaper">Academic Paper</option>
                        <option value="其他" data-i18n="documentManagement.other">Other</option>
                    </select>
                </div>
            </div>

            <!-- 显示选项 -->
            <div class="display-options" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 8px 15px; background: var(--bg-secondary); border-radius: var(--border-radius); font-size: 12px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label data-i18n="documentManagement.itemsPerPage">Items per page:</label>
                    <select id="page-size-select" onchange="changePageSize()" style="padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                        <option value="25">25</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>
                <div id="total-count" style="color: var(--text-muted);" data-i18n="documentManagement.totalDocs">
                    Total 0 documents
                </div>
            </div>

            <!-- 文档统计 -->
            <div class="document-stats" id="document-stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-docs">-</div>
                    <div class="stat-label" data-i18n="documentManagement.totalDocsLabel">Total Documents</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-words">-</div>
                    <div class="stat-label" data-i18n="documentManagement.totalWordsLabel">Total Words</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avg-words">-</div>
                    <div class="stat-label" data-i18n="documentManagement.avgWordsLabel">Average Words</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="recent-docs">-</div>
                    <div class="stat-label" data-i18n="documentManagement.recentDocsLabel">This Week</div>
                </div>
            </div>

            <!-- 批量操作 -->
            <div class="batch-actions" id="batch-actions" style="display: none;">
                <button class="btn-batch-delete" onclick="batchDelete()" data-i18n="documentManagement.batchDelete">🗑️ Batch Delete</button>
                <button class="btn-batch-export" onclick="batchExport()" data-i18n="documentManagement.batchExport">📤 Batch Export</button>
                <div class="selected-count" id="selected-count" data-i18n="documentManagement.selectedCount">Selected 0 documents</div>
            </div>

            <!-- 文档列表 -->
            <div class="document-list">
                <div class="list-header">
                    <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                    <div data-i18n="documentManagement.docTitle">Document Title</div>
                    <div data-i18n="documentManagement.createdTime">Created Time</div>
                    <div data-i18n="documentManagement.wordCount">Word Count</div>
                    <div data-i18n="documentManagement.docType">Type</div>
                    <div data-i18n="documentManagement.actions">Actions</div>
                </div>
                <div id="document-list-body">
                    <div class="loading">
                        <p data-i18n="documentManagement.loadingDocs">Loading documents...</p>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination" id="pagination" style="display: none;">
                <button id="prev-page" onclick="previousPage()" disabled data-i18n="documentManagement.prevPage">Previous</button>
                <span id="page-info" data-i18n="documentManagement.pageInfo">Page 1 of 1</span>
                <button id="next-page" onclick="nextPage()" disabled data-i18n="documentManagement.nextPage">Next</button>
            </div>
        </main>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" data-i18n="documentManagement.confirmDelete">Confirm Delete</h3>
                <button class="modal-close" onclick="closeModal('delete-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <p id="delete-message" data-i18n="documentManagement.deleteMessage">Are you sure you want to delete this document? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" onclick="closeModal('delete-modal')" data-i18n="common.cancel">Cancel</button>
                <button class="btn-danger" onclick="confirmDelete()" data-i18n="common.delete">Delete</button>
            </div>
        </div>
    </div>

    <!-- 文档预览模态框 -->
    <div class="modal" id="preview-modal">
        <div class="modal-content preview-modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="preview-title" data-i18n="documentManagement.previewTitle">Document Preview</h3>
                <button class="modal-close" onclick="closeModal('preview-modal')">&times;</button>
            </div>
            <div class="modal-body preview-modal-body">
                <div class="preview-metadata" id="preview-metadata">
                    <!-- Document metadata will be displayed here -->
                </div>
                <div class="preview-content-wrapper">
                    <h4 data-i18n="documentManagement.documentContent">Document Content</h4>
                    <div class="preview-content" id="preview-content">
                        <!-- Document content will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" onclick="closeModal('preview-modal')" data-i18n="common.close">Close</button>
                <button class="btn-primary" id="analyze-document-btn" data-i18n="documentManagement.analyzeDocument">📊 Analyze Document</button>
            </div>
        </div>
    </div>

    <script>
        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon) {
                if (newTheme === 'dark') {
                    icon.textContent = '☀️';
                } else {
                    icon.textContent = '🌙';
                }
            }
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon && savedTheme === 'dark') {
                icon.textContent = '☀️';
            }
        }

        // 设置默认语言为英文
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof switchLanguage === 'function') {
                switchLanguage('en');
            }
            initTheme();
        });
    </script>
    <script src="/static/js/document-management.js"></script>
</body>
</html>
