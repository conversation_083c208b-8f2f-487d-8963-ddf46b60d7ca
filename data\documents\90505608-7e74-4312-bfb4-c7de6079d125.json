{"doc_id": "90505608-7e74-4312-bfb4-c7de6079d125", "title": "item027_US_DARPA Aims to Develop AI, Autonomy Applications Warfighters Can Trust", "text": "DARPA Aims to Develop AI, Autonomy Applications Warfighters Can Trust\r\nMarch 27, 2024 | <PERSON> <PERSON>, DOD News |   \r\n\r\nAn important goal of the Defense Advanced Research Projects Agency is developing artificial intelligence that is trustworthy for the Defense Department — particularly for making life-or-death recommendations to warfighters, said <PERSON>, deputy director of DARPA's Information Innovation Office.\r\nAI, machine learning and autonomy are being used by about 70% of DARPA's programs in some form or another, <PERSON><PERSON> said today at a Center for Strategic and International Studies event.\r\n\r\n\r\nA man stares into a camera.\r\nAnother reason AI development is such a priority is to prevent an unexpected breakthrough in technology, or \"strategic surprise,\" by adversaries who might also be developing advanced capabilities, he said, adding that DARPA also aims to create its own strategic surprise.\r\n\r\n\r\nSpotlight: Science & Tech\r\nTo accomplish those goals, DARPA is looking for transformative capabilities and ideas from industry and academia, <PERSON><PERSON> said. \r\n\r\nOne of the many ways the agency gets these capabilities and ideas is to hold various types of challenges where teams from the private sector can win prizes worth millions of dollars, he said. \r\n\r\nAn example of that, he said, is DARPA's Artificial Intelligence Cyber Challenge, which uses generative AI technologies — like large language models — to automatically find and fix vulnerabilities in open-source software, particularly software that underlies critical infrastructure.\r\n\r\n\r\nAn unmanned aerial system flies in the sky.\r\nA XQ-58A Valkyrie launches into the sky, creating flames and a trail of orangish smoke.\r\nLarge language models involve processing and manipulating human language to perform such tasks as secure computer coding, decision-making, speech recognition and making predictions.\r\n<PERSON><PERSON> said a unique feature of this challenge is the partnership between DARPA and state-of-the-art large language model providers that are participating in the challenges, including Google, Microsoft, OpenAI and Anthropic. \r\n\r\nMost likely, large language model improvements will also benefit the commercial sector, as well as DOD, Turek said.\r\n\r\n\r\nSpotlight: Engineering in DOD\r\nAn example of the use of autonomy and of AI that DARPA has been testing with the Air Force involves its F-16 fighter jets, he said.\r\n\r\n\r\nA drone operator looks at a computer image.\r\nTurek said DARPA has four areas of AI research involving industry and academia partners:\r\n\r\n1\r\nProficient artificial intelligence;\r\n2\r\nConfidence in the information domain, which includes tools that detect things like manipulated media;\r\n3\r\nSecure and resilient systems; and\r\n4\r\nDefensive and offensive cyber tools.\r\nTurek noted that there's a lot of synergy across those four areas.", "metadata": {"original_filename": "item027_US_DARPA Aims to Develop AI, Autonomy Applications Warfighters Can Trust.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:19.081817", "updated_at": "2025-08-29T02:04:19.081817", "word_count": 2819}