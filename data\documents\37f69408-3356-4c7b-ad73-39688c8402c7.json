{"doc_id": "37f69408-3356-4c7b-ad73-39688c8402c7", "title": "item122_US_Steps Toward AI Governance", "text": "Steps Toward AI Governance\r\n\r\nTechnical Limitations and Constraints on Effective AI Governance\r\nExternal Models Can Be Difficult to Evaluate\r\nThe EqualAI Badge Community agreed that one of the most common technical challenges\r\ncompanies face when evaluating and testing AI models is not knowing how rigorously external\r\nmodels have been evaluated. Specifically, externally acquired models might have undergone\r\ndifferent testing and evaluation from those developed in-house. This knowledge gap about\r\nexternal models stems from a lack of transparency regarding their development process,\r\nincluding their initial design and the data they were trained on. Furthermore, participants noted\r\nthat external models would not have been tested against their organization’s use cases nor\r\nadapted to their needs and preferences. In contrast, customized testing and adjustments would\r\nhave been possible for internally developed models or AI models procured and deployed after\r\nmodifications, such as fine-tuning.\r\nPrioritizing Evaluation of High-Risk Use Cases\r\nDiffering use cases and risk levels present a significant technical challenge for companies in\r\nprioritizing model testing and evaluations. Organizations must carefully identify which use cases\r\npose the highest risks and allocate their limited resources for testing and evaluation accordingly.\r\nWhile the definition of high-risk use cases varies, the EqualAI Badge Community broadly agreed\r\nthat the highest risks include scenarios that could lead to bodily harm, financial loss, release of\r\nsensitive nonpublic data, human resources (HR) violations, illegal outcomes, or denied\r\nopportunities. Defensive applications of AI, such as those used for cybersecurity, are viewed as a\r\ndistinct category. Additionally, participants believed that generative AI could carry particularly\r\nhigh risks, necessitating more-rigorous assessments of such use cases.\r\nHowever, even if risk has been properly profiled, it might still be difficult to develop\r\nstandardized evaluation metrics that capture the vulnerabilities of a generative AI system, given\r\nthe diversity and stochasticity of its outputs. Participants pointed toward more-flexible evaluation\r\nframeworks, such as red teaming or active user feedback, as mechanisms to identify failure\r\nmodes from generative systems that traditional metrics might not capture.\r\nOrganizational Limitations and Constraints on Effective AI Governance\r\nMisaligned Incentives Might Slow or Impede AI Governance\r\nParticipants highlighted how misaligned organizational goals can create disincentives for\r\ninvesting the significant resources required to implement appropriate AI processes. For instance,\r\nsales and engineering teams might feel pressure to deliver products quickly, making it\r\nchallenging to integrate AI best practices, such as documentation for transparency, into an\r\nalready high-pressure environment. Gaining buy-in for AI governance initiatives is particularly\r\n4\r\ndifficult when these practices are not legally mandated. Additionally, there are few incentives for\r\nemployees to voice concerns, and whistleblower protections are lacking. In such an environment,\r\nrisks can be overlooked. A changing regulatory landscape, however, could significantly shift this\r\nrisk environment. For example, new state-level or international regulations could alter incentive\r\nstructures for AI governance. As new regulations emerge, research will be needed to understand\r\ntheir potential impact.\r\nCompany Culture Sets the Tone\r\nCompany culture is a key component of AI governance. The EqualAI Badge Community\r\nemphasized that cultivating a supportive culture is essential for upholding AI principles,\r\nrequiring time and commitment to establish effective norms. Shifting the perception of AI\r\ngovernance from that of an unnecessary burden to an essential asset is crucial for teams to\r\nachieve their goals efficiently.\r\nTeams must recognize the value of AI governance, including strengthening business\r\npartnerships, preventing costly errors, fostering thought leadership, and attracting top talent with\r\nrelevant skills. Conversely, a lack of effective AI governance could result in legal liabilities,\r\nreputational penalties, and/or financial penalties.\r\nThe EqualAI Badge Community agreed that an AI governance team needs authority to have\r\nimpact. As they put it, one factor that contributes to the perception that AI governance teams are\r\n“not critical” is when they lack “no-go” authority or a “kill switch” to halt product releases or\r\nongoing processes. Typically, concerns must be validated by legal and compliance teams who do\r\npossess such authority before action is taken to slow or stop operations. In general, the authority\r\nto restrict a product release might need a clear, impending harm to justify the cost to agility, and\r\nthe absence of AI-specific laws and regulations in the United States and elsewhere might make it\r\nharder to demonstrate these harms.\r\nLeadership Buy-In Is Essential for Sustainable Governance\r\nLeadership support is crucial for fostering an organizational culture that embraces AI\r\ngovernance. While initiatives can start from the bottom up, true sustainability hinges on buy-in\r\nfrom top leadership. This support should extend beyond a single leader to encompass the entire\r\nboard and C-suite executives. Moreover, given the potential for leadership turnover, securing\r\nbuy-in from multiple stakeholders is essential for long-term sustainability.\r\nEmployee Buy-In Is Crucial\r\nAccording to participants, a significant challenge in adopting AI governance can stem from\r\nemployee fears of job displacement, which could foster a culture of hesitance toward AI\r\nadoption. This resistance could be exacerbated by a lack of AI literacy throughout the\r\norganization. Training employees on AI is challenging because training is likely most effective\r\nwhen provided to those who specifically need such training to do their jobs, such as those who\r\n5\r\nuse AI tools or who make strategic decisions about investing in AI technology. Measuring the\r\nsuccess of any AI training presents a related challenge and starts with understanding what level\r\nof AI literacy is needed for a given role.\r\nIncreasing employee comfort with AI is also dependent on organizational goals and\r\nincentives. If there are few opportunities for employees to learn about AI governance and best\r\npractices, this could contribute to a lack of consensus about how AI systems should be\r\nimplemented or governed.\r\nVendor Relations Should Be Strengthened\r\nProductive vendor relationships can be hindered by an overall lack of technical knowledge\r\nabout a given AI model (e.g., model architecture, testing and validation results). Procurement\r\nteams might not know which technical questions to ask about AI governance, while vendors\r\nmight be ill-equipped to provide the necessary insights on such crucial aspects as metrics and\r\nmodel transparency. The risk that a generative AI system poses, for example, might depend\r\nheavily on the use case and environment in which it is deployed, further complicating matters.\r\nTherefore, vendors could be caught in situations in which they must develop a suite of metrics\r\nthat are applicable to consumers across business domains while being specific and actionable\r\nenough for the customized use cases and risk profiles of an individual customer. This gap in\r\nfoundational understanding and consistent inquiry leads to miscommunication and missed\r\nopportunities for ensuring effective AI development and testing.\r\nRecommendations\r\nThe EqualAI Badge Community proposed several solutions to address the challenges of\r\nimplementing AI governance, encompassing recommendations for both companies and\r\ngovernment bodies.\r\nRecommendations for Companies\r\nCatalog AI Use Cases\r\nCompanies should maintain a centralized catalog of AI tools and their applications, and the\r\ncatalog should be regularly updated to track use across the organization. This catalog should\r\ndocument key specifications and facilitate risk assessment, prioritizing use cases based on such\r\nfactors as potential financial loss, HR violations, illegal outcomes, or bodily harm. By clarifying\r\nuses and their risk levels, organizations can promote transparency and informed decisionmaking\r\nregarding AI.\r\nStandardize Vendor Questions\r\nDeveloping a standardized set of relevant questions for vendors would enable companies to\r\nalign on vendor evaluations that are based on standardized metrics. Vendors would be aware\r\n6\r\nof—and incentivized to meet—key expectations. This approach can facilitate better integration\r\nof aligned practices into vendor model development, promoting AI governance across business\r\nrelationships.\r\nCreate an AI Information Tool\r\nImplementing an internal information tool, such as a chatbot, that would provide a\r\ncompany’s employees with clear answers to AI governance questions could serve as a valuable\r\nresource. This chatbot could be trained with relevant information from government, industry, and\r\nnonprofit sources and could be tailored to the company’s needs and use cases.\r\nFoster Multistakeholder Engagement\r\nCompanies should prioritize both internal and external engagement regarding their AI\r\npractices. Internally, this involves securing C-suite support and fostering a culture of AI\r\ngovernance through education and open communication. Externally, organizations should solicit\r\nfeedback from a broad set of stakeholders, particularly unanticipated end users and those from\r\nhistorically marginalized communities, to understand and mitigate the impacts of AI\r\ndeployments that might not have been captured in the development process. Additionally,\r\nthoroughly documenting these actions throughout the AI life cycle will enhance transparency and\r\naid in cross-enterprise and regulatory discussions.\r\nLeverage Existing Processes\r\nOrganizations can apply such established processes as crisis management to handle AIrelated incidents (e.g., data leaks) and technical risk management (e.g., code reviews, stress\r\ntesting) to support AI governance. This approach encourages efficiency by integrating AI\r\ngovernance practices into existing frameworks rather than creating new ones.\r\nRecommendations for the Federal Government\r\nPromote a Consistent Approach\r\nAccording to summit participants, the federal government should legislate, foster, or\r\nincentivize the establishment of a consistent regulatory framework that would help organizations\r\nnavigate the complexities and standardize the management of AI governance across various\r\nstates and international jurisdictions. This might involve clarifying existing laws and providing\r\nnew regulations and laws that govern the use of AI tools specifically. The EqualAI Badge\r\nCommunity highlighted a desire for governments to balance swift regulatory action with the\r\npromotion of innovation as AI technologies continue to evolve.\r\n7\r\nConclusion\r\nThe 2024 EqualAI summit underscored the critical importance of fostering a collaborative\r\nenvironment for effective AI governance. As the adoption of AI technologies accelerates,\r\norganizations must move beyond isolated efforts to implement, evaluate, and manage their AI\r\nsystems. Going forward, organizations should engage in shared learning to effectively tackle the\r\ncomplex AI-related challenges that they face. Summit discussions highlighted a broad array of\r\napproaches, tools, and frameworks that are being employed but also revealed significant gaps\r\nand limitations in technical evaluation, organizational culture, and employee engagement.\r\nTo appropriately navigate the fast-moving landscape of AI, organizations need to prioritize\r\nbuilding a culture that values best practices and is supported by leadership across all levels. This\r\neffort entails investing in AI literacy, standardizing vendor expectations, and aligning\r\norganizational goals with AI governance principles. By fostering an environment in which\r\nknowledge and best practices can be shared, businesses can not only mitigate risks but also\r\nunlock the full potential of AI technologies.\r\nLooking ahead, the insights from the third EqualAI summit offer considerations for\r\norganizations aiming to implement effective structures for AI governance. This imperative is not\r\nmerely a regulatory need but a strategic imperative that can enhance innovation, build trust, and\r\nfoster sustainable outcomes. Key recommendations from the summit can support best practices\r\nto incorporate and standardize in companies across the country and globe, such as aligning\r\nvendor questions, creating an AI information tool, and maintaining a centralized catalog of AI\r\nuse cases. Fostering multistakeholder engagement and leveraging existing organizational\r\nprocesses on which to establish AI governance practices are key elements of ensuring long-term\r\nsuccess in this critical effort.\r\nBuilding a set of resources or a community to share best practices and lessons learned is,\r\nthus, critical to sustaining AI governance efforts. Several summit participants mentioned that,\r\nfrom their perspectives, one of the most-valuable experiences was the opportunity to meet and\r\nshare insights with peers working on corporate AI governance. The journey toward widespread\r\neffective use of AI is ongoing, and collaboration across disciplines and industry sectors will be\r\nessential in shaping a future in which AI serves as a positive force for all of society. ", "metadata": {"original_filename": "item122_US_Steps Toward AI Governance.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:20.022027", "updated_at": "2025-08-29T02:04:20.022027", "word_count": 13422}