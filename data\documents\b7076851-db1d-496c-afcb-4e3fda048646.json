{"doc_id": "b7076851-db1d-496c-afcb-4e3fda048646", "title": "item228_US_Update to the 2016 National Artificial Intelligence Research and Development Strategic Plan RFI Responses", "text": "Update to the 2016 National Artificial Intelligence Research and Development Strategic Plan RFI Responses \r\n\r\nStrategy 2: Develop effective methods for human-AI collaboration Cooperative communication: While synthetic speech, voice to text, statistical Natural Language Processing (what word will come next) and semantic embedding functions have allowed for various forms of crude human-AI communication, we assert that this is just the beginning. (<PERSON><PERSON>, 2010) argues that human communication is unique due to its cooperative nature. A summary of how such capabilities may have evolved is as follows:   1) The great apes are able to use intentional gestures to request or demand actions from others. 2) In contrast, early humans were able to use gestures (pointing and pantomime) to accomplish three basic goals: i) requests for actions from others, ii) to give information that might be of assistance to others and iii) to express one’s feelings regarding the environment. 3) The ability to both request and offer assistance can be seen as a form of cooperative communication that involves mechanisms such as common conceptual ground, joint intentionality and various forms of recursive “mind reading”.  4) The drive towards expressing one’s feelings can be viewed as a mechanism for building common conceptual ground with the goal of minimizing within tribe differences and maximizes between tribe differences. You might be willing to cooperate with a non-tribe member, but you generally only share your admiration for a pleasant sunset with a member of your clan.  5) While pointing allows for identifying objects within the current field of view, pantomime allows for identification of non-visual referents. 6) As these pantomimes become conventionalized they can become increasingly arbitrary in nature. This allows for development of spoken language which is based on completely arbitrary but socially accepted symbols. 7) Arbitrary languages (both spoken and sign) have the complexity required to construct narratives which can describe events and actions of multiple agents over both space and time. Various frameworks can then emerge so that these sequences of events can appear to be causal or even “make sense”.  8) Through this analysis we see that cognitive skills evolve phylogenetically (over the lifetime of the species), enabling the creation of cultural products historically, which then provide developing children with the biological and cultural tools they need to develop ontogenetically (over the lifetime of the organism).  Consider the following short conversation: A: “Do you want to see a movie tonight?” B: “I have a math test tomorrow morning”. In order to support this level of interaction the fact that a test requires studying the night before must be part of the conceptual common ground shared by both A and B. B must know that A is aware of this fact and B must know that A is aware of B’s knowledge of A… This form of almost endless recursive mind-reading allows A to infer B’s intentions regarding the movie proposal without B explicitly saying yes or no.  Recommendation: The ability to establish a conceptual common ground, recursive mind-reading and the transformation of chronologies into meaningful narratives must all be included in the repertoire of a collaborative-AI.   Towards a zero-marginal cost economy: Rifkin (Rifkin, 2014) heralds a future where marginal costs (the cost of producing additional units of a product or service once initial fixed costs have been accounted for) shrink asymptotically to zero. The cost of living of US workers will be significantly reduced if the ability to produce anything, anywhere at almost no cost can be established on a locality by locality basis. Whole communities will be able to decouple themselves from reliance on 21st century global manufacturing. Key zero-marginal cost technologies will include: 1) The cost of raw materials: Recycling of waste produced by zero-marginal-cost communities. 2) The cost of physical labor: Robots gifted with the ability to learn and perform any physical task. 3) The cost of specialized manufacturing: 3D printing capable of producing any type of object. 4) The cost of energy: Renewables including solar and wind. 5) The cost of research and management: AI. Recommendation: If AI research can be part of a transformation towards a zero-marginal cost economy, many of the potential calamities, both physical and political, that currently face humanity can be reduced or even averted entirely  (Suzuky & Atwood, 2010).  Strategy 3: Understand and address the ethical, legal, and societal implications of AI Ethical Instantiation: Authors such as (Trivers, 2014) and (Ridley, 1998) argue that our ethical systems may have naturally arisen due to mechanisms such as game theoretic dynamics, cultural selection, shared intentions and the virtue of cooperation. Humans are the only species to have discovered David Ricardo’s law of comparative advantage.  Richard Dawkins argues that by being aware of the mechanisms that have forged our nature, we can become more reflective and thus transcend the question of can altruism really exist and possibly become truly virtuous. Knowledge of the greedy gene may liberate us from its tyranny. Can we then instill such virtue in to our AI? Reasoning over ethical dilemmas: Autonomous agents will have to make life or death decisions. How can we equip these agents with the wherewithal to face such challenges? Should a runaway autonomous train elect to derail itself and possibly kill its passengers in order to avoid barreling into densely populated urban center?  If an aluminum smelter is deprived of energy for more than six hours, miles of molten metal will solidify resulting in billions of dollars of damage. Given such a crisis, should an AI agent divert energy from a near-by hospital possibly depriving patients of critical care? Through access to vast data archives, an AI Agent deduces that a single mother with three children is in a poor bargaining position with few options. Should it exploit the situation by raising the price of its merchandise? While the last scenario may not result in a mortality, it has bearing on the type of society that we would like to live in. Snakes in Suites: In (Ronson, 2012) we find that persons with psychopathic tendencies occur in the general population with a frequency of approximately 1 in 100. In Corporate America the statistics are closer to 3 in 100. Bob Hare has established the following check list for the purpose of identifying such individuals: glib and superficial charm, grandiose self-worth, proneness to boredom, pathological lying, conning and manipulative, lack of remorse or guilt, shallow affect, lack of empathy, parasitic lifestyle, poor behavior controls, promiscuous, early behavior problems, lack of realistic long term goals, impulsive, irresponsible, cannot accept responsibility for own actions, many short term relationships, juvenile delinquency and criminal versatility. Throughout history these villains have been the cause of great sorrow and pain. Modern AI could be used to actively identify and neutralize such predators. However, the ethical question that AI protectors of society must address is: should they?  The human condition is a spectrum and as Rene Char put it “We must all develop your own legitimate strangeness”.  Recommendation: Similar to a consortium focused on questions of epistemology, a working group concerned with the ethics of AI should be established. In addition to members of Industry and Accademia, stakeholders such as the clergy, the judiciary as well as advocates for vulnerable populations should be included.    Strategy 4: Ensure the safety and security of AI systems The busy child, our reconfigurable minds and malicious AI: Barrat (Barrat, 2015) starts with a description of the busy child scenario where an AI with access to unlimited resources enters in to a state of monotonically increasing intelligence culminating in a form of artificial super intelligence. Given the fact that examples of a stronger species being concerned with the welfare of a weaker one, are practically non-existent, it is argued that physical containment measures should be considered in order to combat such dooms day scenarios. Alternatively, authors such as (Postman, 1986) and (Bauerlein, 2009) argue that exposure to economically and/or politically motivated media and AI Agents equipped with tools such as the ability to synthesis false information, novelty-based addiction and automatic herding of the unaware, may be extremely hazardous. Given the global and distributed nature of our integrated economies, issues associated with black box IP and undetected tampering must also be addressed. Seemingly benign AI may harbor nefarious intentions. Can AI watch dogs be used to identify malicious AI activity? Is it possible to insert suspicious AI Agents into seemingly real but physically isolated environments so that their true purposes can be discerne", "metadata": {"original_filename": "item228_US_Update to the 2016 National Artificial Intelligence Research and Development Strategic Plan RFI Responses.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:20.859870", "updated_at": "2025-08-29T02:04:20.859870", "word_count": 9032}