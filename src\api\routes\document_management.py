#!/usr/bin/env python3
"""
📁 文档管理API路由 - 增强版文档管理接口
提供文档的高级管理功能，包括搜索、分类、批量操作等
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

from src.models.schemas import DocumentResponse, DocumentCreate
from src.services.document_service import DocumentService, get_document_service
from src.core.auth import auth_deps

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v2/documents", tags=["文档管理"])

class DocumentFilter(BaseModel):
    """文档过滤器"""
    title_contains: Optional[str] = Field(None, description="标题包含的关键词")
    content_contains: Optional[str] = Field(None, description="内容包含的关键词")
    created_after: Optional[datetime] = Field(None, description="创建时间晚于")
    created_before: Optional[datetime] = Field(None, description="创建时间早于")
    min_word_count: Optional[int] = Field(None, description="最小字数")
    max_word_count: Optional[int] = Field(None, description="最大字数")
    metadata_filters: Optional[Dict[str, Any]] = Field(None, description="元数据过滤条件")

class DocumentListRequest(BaseModel):
    """文档列表请求"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", description="排序方向: asc/desc")
    filters: Optional[DocumentFilter] = Field(None, description="过滤条件")

class DocumentListResponse(BaseModel):
    """文档列表响应"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")
    documents: List[DocumentResponse] = Field(..., description="文档列表")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

class DocumentSearchRequest(BaseModel):
    """文档搜索请求"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    search_in: List[str] = Field(default=["title", "content"], description="搜索范围: title, content, metadata")
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    highlight: bool = Field(default=True, description="是否高亮搜索结果")

class DocumentSearchResponse(BaseModel):
    """文档搜索响应"""
    total: int = Field(..., description="搜索结果总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    query: str = Field(..., description="搜索关键词")
    results: List[Dict[str, Any]] = Field(..., description="搜索结果")
    search_time: float = Field(..., description="搜索耗时(秒)")

class DocumentStatsResponse(BaseModel):
    """文档统计响应"""
    total_documents: int = Field(..., description="文档总数")
    total_word_count: int = Field(..., description="总字数")
    avg_word_count: float = Field(..., description="平均字数")
    documents_by_date: Dict[str, int] = Field(..., description="按日期统计")
    documents_by_type: Dict[str, int] = Field(..., description="按类型统计")
    recent_activity: List[Dict[str, Any]] = Field(..., description="最近活动")

class BatchOperationRequest(BaseModel):
    """批量操作请求"""
    document_ids: List[str] = Field(..., min_items=1, description="文档ID列表")
    operation: str = Field(..., description="操作类型: delete, update_metadata, export")
    parameters: Optional[Dict[str, Any]] = Field(None, description="操作参数")

class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    success: bool = Field(..., description="操作是否成功")
    total: int = Field(..., description="总操作数")
    succeeded: int = Field(..., description="成功数")
    failed: int = Field(..., description="失败数")
    results: List[Dict[str, Any]] = Field(..., description="详细结果")
    operation: str = Field(..., description="操作类型")

@router.post("/list", response_model=DocumentListResponse)
async def list_documents_advanced(
    request: DocumentListRequest,
    document_service: DocumentService = Depends(get_document_service)
):
    """高级文档列表查询"""
    try:
        start_time = datetime.now()
        
        # 获取所有文档（在实际项目中应该使用数据库查询）
        all_documents = await document_service.list_documents(0, 10000)  # 临时获取所有文档
        
        # 应用过滤器
        filtered_docs = all_documents
        if request.filters:
            filtered_docs = await _apply_filters(all_documents, request.filters)
        
        # 排序
        filtered_docs = await _sort_documents(filtered_docs, request.sort_by, request.sort_order)
        
        # 分页
        total = len(filtered_docs)
        start_idx = (request.page - 1) * request.page_size
        end_idx = start_idx + request.page_size
        page_docs = filtered_docs[start_idx:end_idx]
        
        total_pages = (total + request.page_size - 1) // request.page_size
        
        logger.info(f"高级文档列表查询完成，返回 {len(page_docs)} 个文档")
        
        return DocumentListResponse(
            total=total,
            page=request.page,
            page_size=request.page_size,
            total_pages=total_pages,
            documents=page_docs,
            has_next=request.page < total_pages,
            has_prev=request.page > 1
        )
        
    except Exception as e:
        logger.error(f"高级文档列表查询失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询失败: {str(e)}"
        )

@router.post("/search", response_model=DocumentSearchResponse)
async def search_documents(
    request: DocumentSearchRequest,
    document_service: DocumentService = Depends(get_document_service)
):
    """文档全文搜索"""
    try:
        start_time = datetime.now()
        
        # 获取所有文档进行搜索（在实际项目中应该使用全文搜索引擎）
        all_documents = await document_service.list_documents(0, 10000)
        
        # 执行搜索
        search_results = await _search_documents(all_documents, request)
        
        # 分页
        total = len(search_results)
        start_idx = (request.page - 1) * request.page_size
        end_idx = start_idx + request.page_size
        page_results = search_results[start_idx:end_idx]
        
        search_time = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"文档搜索完成，关键词: {request.query}，找到 {total} 个结果")
        
        return DocumentSearchResponse(
            total=total,
            page=request.page,
            page_size=request.page_size,
            query=request.query,
            results=page_results,
            search_time=search_time
        )
        
    except Exception as e:
        logger.error(f"文档搜索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )

@router.get("/stats", response_model=DocumentStatsResponse)
async def get_document_statistics(
    document_service: DocumentService = Depends(get_document_service)
):
    """获取文档统计信息"""
    try:
        # 获取所有文档
        all_documents = await document_service.list_documents(0, 10000)
        
        # 计算统计信息
        stats = await _calculate_document_stats(all_documents)
        
        logger.info("文档统计信息计算完成")
        return stats
        
    except Exception as e:
        logger.error(f"获取文档统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计失败: {str(e)}"
        )

@router.post("/batch-operation", response_model=BatchOperationResponse)
async def batch_operation(
    request: BatchOperationRequest,
    document_service: DocumentService = Depends(get_document_service)
):
    """批量文档操作"""
    try:
        logger.info(f"开始批量操作: {request.operation}，文档数: {len(request.document_ids)}")
        
        results = []
        succeeded = 0
        failed = 0
        
        for doc_id in request.document_ids:
            try:
                if request.operation == "delete":
                    success = await document_service.delete_document(doc_id)
                    if success:
                        succeeded += 1
                        results.append({"doc_id": doc_id, "status": "success", "message": "删除成功"})
                    else:
                        failed += 1
                        results.append({"doc_id": doc_id, "status": "failed", "message": "文档不存在"})
                        
                elif request.operation == "update_metadata":
                    # 更新元数据操作
                    doc = await document_service.get_document(doc_id)
                    if doc:
                        # 这里需要实现元数据更新逻辑
                        succeeded += 1
                        results.append({"doc_id": doc_id, "status": "success", "message": "元数据更新成功"})
                    else:
                        failed += 1
                        results.append({"doc_id": doc_id, "status": "failed", "message": "文档不存在"})
                        
                elif request.operation == "export":
                    # 导出操作
                    doc = await document_service.get_document(doc_id)
                    if doc:
                        succeeded += 1
                        results.append({"doc_id": doc_id, "status": "success", "message": "导出成功"})
                    else:
                        failed += 1
                        results.append({"doc_id": doc_id, "status": "failed", "message": "文档不存在"})
                        
                else:
                    failed += 1
                    results.append({"doc_id": doc_id, "status": "failed", "message": f"不支持的操作: {request.operation}"})
                    
            except Exception as e:
                failed += 1
                results.append({"doc_id": doc_id, "status": "failed", "message": str(e)})
        
        logger.info(f"批量操作完成: 成功 {succeeded}，失败 {failed}")
        
        return BatchOperationResponse(
            success=failed == 0,
            total=len(request.document_ids),
            succeeded=succeeded,
            failed=failed,
            results=results,
            operation=request.operation
        )
        
    except Exception as e:
        logger.error(f"批量操作失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量操作失败: {str(e)}"
        )

# 辅助函数
async def _apply_filters(documents: List[DocumentResponse], filters: DocumentFilter) -> List[DocumentResponse]:
    """应用过滤条件"""
    filtered = documents
    
    if filters.title_contains:
        filtered = [doc for doc in filtered if filters.title_contains.lower() in doc.title.lower()]
    
    if filters.content_contains:
        filtered = [doc for doc in filtered if filters.content_contains.lower() in doc.text.lower()]
    
    if filters.created_after:
        filtered = [doc for doc in filtered if doc.created_at >= filters.created_after]
    
    if filters.created_before:
        filtered = [doc for doc in filtered if doc.created_at <= filters.created_before]
    
    if filters.min_word_count:
        filtered = [doc for doc in filtered if doc.word_count >= filters.min_word_count]
    
    if filters.max_word_count:
        filtered = [doc for doc in filtered if doc.word_count <= filters.max_word_count]
    
    return filtered

async def _sort_documents(documents: List[DocumentResponse], sort_by: str, sort_order: str) -> List[DocumentResponse]:
    """排序文档"""
    reverse = sort_order.lower() == "desc"

    if sort_by == "created_at":
        return sorted(documents, key=lambda x: x.created_at, reverse=reverse)
    elif sort_by == "updated_at":
        return sorted(documents, key=lambda x: x.updated_at, reverse=reverse)
    elif sort_by == "title":
        return sorted(documents, key=lambda x: x.title, reverse=reverse)
    elif sort_by == "word_count":
        return sorted(documents, key=lambda x: x.word_count, reverse=reverse)
    else:
        return documents

async def _search_documents(documents: List[DocumentResponse], request: DocumentSearchRequest) -> List[Dict[str, Any]]:
    """搜索文档"""
    results = []
    query_lower = request.query.lower()

    for doc in documents:
        score = 0
        highlights = {}

        # 在标题中搜索
        if "title" in request.search_in and query_lower in doc.title.lower():
            score += 10
            if request.highlight:
                highlights["title"] = _highlight_text(doc.title, request.query)

        # 在内容中搜索
        if "content" in request.search_in and query_lower in doc.text.lower():
            score += 5
            if request.highlight:
                highlights["content"] = _highlight_text(doc.text[:500], request.query)  # 只取前500字符

        # 在元数据中搜索
        if "metadata" in request.search_in:
            metadata_str = str(doc.metadata).lower()
            if query_lower in metadata_str:
                score += 3

        if score > 0:
            result = {
                "document": doc.dict(),
                "score": score,
                "highlights": highlights
            }
            results.append(result)

    # 按相关性排序
    results.sort(key=lambda x: x["score"], reverse=True)
    return results

def _highlight_text(text: str, query: str, max_length: int = 200) -> str:
    """高亮搜索关键词"""
    query_lower = query.lower()
    text_lower = text.lower()

    # 找到关键词位置
    pos = text_lower.find(query_lower)
    if pos == -1:
        return text[:max_length] + "..." if len(text) > max_length else text

    # 计算摘要范围
    start = max(0, pos - max_length // 2)
    end = min(len(text), start + max_length)

    # 提取摘要
    snippet = text[start:end]

    # 高亮关键词
    highlighted = snippet.replace(query, f"<mark>{query}</mark>")

    # 添加省略号
    if start > 0:
        highlighted = "..." + highlighted
    if end < len(text):
        highlighted = highlighted + "..."

    return highlighted

async def _calculate_document_stats(documents: List[DocumentResponse]) -> DocumentStatsResponse:
    """计算文档统计信息"""
    total_documents = len(documents)
    total_word_count = sum(doc.word_count for doc in documents)
    avg_word_count = total_word_count / total_documents if total_documents > 0 else 0

    # 按日期统计
    documents_by_date = {}
    for doc in documents:
        date_str = doc.created_at.strftime("%Y-%m-%d")
        documents_by_date[date_str] = documents_by_date.get(date_str, 0) + 1

    # 按类型统计（基于元数据）
    documents_by_type = {}
    for doc in documents:
        doc_type = doc.metadata.get("type", "未分类")
        documents_by_type[doc_type] = documents_by_type.get(doc_type, 0) + 1

    # 最近活动（最近7天的文档）
    recent_activity = []
    seven_days_ago = datetime.now() - timedelta(days=7)
    recent_docs = [doc for doc in documents if doc.created_at >= seven_days_ago]

    for doc in sorted(recent_docs, key=lambda x: x.created_at, reverse=True)[:10]:
        recent_activity.append({
            "doc_id": doc.doc_id,
            "title": doc.title,
            "created_at": doc.created_at.isoformat(),
            "word_count": doc.word_count
        })

    return DocumentStatsResponse(
        total_documents=total_documents,
        total_word_count=total_word_count,
        avg_word_count=round(avg_word_count, 2),
        documents_by_date=documents_by_date,
        documents_by_type=documents_by_type,
        recent_activity=recent_activity
    )
