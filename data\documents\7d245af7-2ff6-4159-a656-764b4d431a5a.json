{"doc_id": "7d245af7-2ff6-4159-a656-764b4d431a5a", "title": "item003_US_NIST Risk Management Framework Aims to Improve Trustworthiness of Artificial Intelligence", "text": "NIST Risk Management Framework Aims to Improve Trustworthiness of Artificial Intelligence\r\nNew guidance seeks to cultivate trust in AI technologies and promote AI innovation while mitigating risk.\r\nJanuary 26, 2023\r\nShare\r\nFacebook\r\nLinkedin\r\nX.com\r\nEmail\r\nCircular design includes elements of the AI Risk Management Framework: Govern, Measure, Manage, Map.\r\nCredit: N. Hanacek/NIST\r\nWASHINGTON — The U.S. Department of Commerce’s National Institute of Standards and Technology (NIST) has released its Artificial Intelligence Risk Management Framework (AI RMF 1.0), a guidance document for voluntary use by organizations designing, developing, deploying or using AI systems to help manage the many risks of AI technologies. \r\n\r\nThe AI RMF follows a direction from Congress for NIST to develop the framework and was produced in close collaboration with the private and public sectors. It is intended to adapt to the AI landscape as technologies continue to develop, and to be used by organizations in varying degrees and capacities so that society can benefit from AI technologies while also being protected from its potential harms.\r\n\r\n“This voluntary framework will help develop and deploy AI technologies in ways that enable the United States, other nations and organizations to enhance AI trustworthiness while managing risks based on our democratic values,” said Deputy Commerce Secretary <PERSON>. “It should accelerate AI innovation and growth while advancing — rather than restricting or damaging — civil rights, civil liberties and equity for all.” \r\n\r\nCompared with traditional software, AI poses a number of different risks. AI systems are trained on data that can change over time, sometimes significantly and unexpectedly, affecting the systems in ways that can be difficult to understand. These systems are also “socio-technical” in nature, meaning they are influenced by societal dynamics and human behavior. AI risks can emerge from the complex interplay of these technical and societal factors, affecting people’s lives in situations ranging from their experiences with online chatbots to the results of job and loan applications.   \r\n\r\nThe framework equips organizations to think about AI and risk differently. It promotes a change in institutional culture, encouraging organizations to approach AI with a new perspective — including how to think about, communicate, measure and monitor AI risks and its potential positive and negative impacts. \r\n\r\nThe new framework should “accelerate AI innovation and growth while advancing — rather than restricting or damaging — civil rights, civil liberties and equity for all.” —Deputy Commerce Secretary Don Graves\r\n\r\nThe AI RMF provides a flexible, structured and measurable process that will enable organizations to address AI risks. Following this process for managing AI risks can maximize the benefits of AI technologies while reducing the likelihood of negative impacts to individuals, groups, communities, organizations and society.\r\n\r\nThe framework is part of NIST’s larger effort to cultivate trust in AI technologies — necessary if the technology is to be accepted widely by society, according to Under Secretary for Standards and Technology and NIST Director Laurie E. Locascio. \r\n\r\n“The AI Risk Management Framework can help companies and other organizations in any sector and any size to jump-start or enhance their AI risk management approaches,” Locascio said. “It offers a new way to integrate responsible practices and actionable guidance to operationalize trustworthy and responsible AI. We expect the AI RMF to help drive development of best practices and standards.”\r\n\r\nThe AI RMF is divided into two parts. The first part discusses how organizations can frame the risks related to AI and outlines the characteristics of trustworthy AI systems. The second part, the core of the framework, describes four specific functions — govern, map, measure and manage — to help organizations address the risks of AI systems in practice. These functions can be applied in context-specific use cases and at any stages of the AI life cycle.\r\n\r\nWorking closely with the private and public sectors, NIST has been developing the AI RMF for 18 months. The document reflects about 400 sets of formal comments NIST received from more than 240 different organizations on draft versions of the framework. NIST today released statements from some of the organizations that have already committed to use or promote the framework.\r\n\r\nThe agency also today released a companion voluntary AI RMF Playbook, which suggests ways to navigate and use the framework. \r\n\r\nNIST plans to work with the AI community to update the framework periodically and welcomes suggestions for additions and improvements to the playbook at any time. Comments received by the end of February 2023 will be included in an updated version of the playbook to be released in spring 2023.\r\n\r\nIn addition, NIST plans to launch a Trustworthy and Responsible AI Resource Center to help organizations put the AI RMF 1.0 into practice. The agency encourages organizations to develop and share profiles of how they would put it to use in their specific contexts. Submissions may be <NAME_EMAIL>.\r\n\r\nNIST is committed to continuing its work with companies, civil society, government agencies, universities and others to develop additional guidance. The agency today issued a roadmap for that work.\r\n\r\nThe framework is part of NIST’s broad and growing portfolio of AI-related work that includes fundamental and applied research along with a focus on measurement and evaluation, technical standards, and contributions to AI policy.  ", "metadata": {"original_filename": "item003_US_NIST Risk Management Framework Aims to Improve Trustworthiness of Artificial Intelligence.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:18.725700", "updated_at": "2025-08-29T02:04:18.725700", "word_count": 5664}