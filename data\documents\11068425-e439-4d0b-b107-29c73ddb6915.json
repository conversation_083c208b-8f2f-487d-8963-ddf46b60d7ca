{"doc_id": "11068425-e439-4d0b-b107-29c73ddb6915", "title": "item126_US_A Model for Regulating AI", "text": "A Model for Regulating AI\r\n\r\nArtificial intelligence is advancing so rapidly that many who have been part of its development are now among the most vocal about the need to regulate it. While AI will bring many benefits, it is also potentially dangerous; it could be used to create cyber or bio weapons or to launch massive disinformation attacks. And if an AI is stolen or leaked even once, it could be impossible to prevent it from spreading throughout the world.\r\n\r\nThese concerns are not hypothetical. Such a leak has, in fact, already occurred. In March, an AI model developed by Meta called LLaMA appeared online. LLaMA was not intended to be publicly accessible, but the model was shared with AI researchers, who then requested full access to further their own projects. At least two of them abused Meta's trust and released the model online, and Meta has been unable to remove LLaMA from the internet. The model can still be accessed by anyone.\r\n\r\nFortunately, LLaMA is relatively harmless. While it could be used to launch spear-phishing attacks (PDF), there is not yet cause for major alarm. The theft or leak of more capable AI models would be much worse. But the risks can be substantially reduced with effective oversight of three parts of the AI supply chain: hardware, training, and deployment.\r\n\r\nWhile AI will bring many benefits, it is also potentially dangerous; it could be used to create cyber or bio weapons or to launch massive disinformation attacks.\r\n\r\nThe first is hardware. The creation of advanced AI models requires thousands of specialized microchips, costing tens or even hundreds of millions of dollars. Only a few companies—such as Nvidia and AMD—design these chips, and most are sold to large cloud-computing providers such as Amazon, Microsoft, and Google, as well as the U.S. government, a handful of foreign governments, and just a few other deep-pocketed tech companies. Because the pool of buyers is so small, a federal regulator could track and license large concentrations of AI chips. And cloud providers, who own the largest clusters of AI chips, could be subject to “know your customer” requirements so that they identify clients who place huge rental orders that signal an advanced AI system is being built.\r\n\r\nThe next stage of AI oversight focuses on the training of each model. A developer can—and should be required to—assess a model's risky capabilities during training. Problems detected early can be more easily fixed, so a safer, less-expensive final product can be built in less time.\r\n\r\nOnce training is complete, a powerful AI model should be subject to rigorous review by a regulator or third-party evaluator before it is released to the world. Expert red teams, pretending to be malicious adversaries, can try to make the AI perform unintended behaviors, including the design of weapons. Systems that exhibit dangerous capabilities should not be released until safety can be assured.\r\n\r\nAI regulation is already underway in Britain, the European Union, and China. But many breakthrough models—and most of the advanced AI systems that have brought us to this moment—have been developed in the United States. We would do well to establish a model of oversight for the world that focuses on the three parts of the AI supply chain. Increasing the safety of the American AI industry would boost public confidence at a time when consumers are growing wary of just what sort of future AI might bring", "metadata": {"original_filename": "item126_US_A Model for Regulating AI.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:20.054024", "updated_at": "2025-08-29T02:04:20.054024", "word_count": 3449}