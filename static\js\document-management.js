// 文档管理系统 JavaScript

// 全局变量
let currentPage = 1;
let pageSize = 50;  // 增加每页显示数量
let totalPages = 1;
let allDocuments = [];
let filteredDocuments = [];
let selectedDocuments = new Set();
let currentSort = { field: 'created_at', order: 'desc' };
let currentFilters = {};

// DOM 元素
const documentListBody = document.getElementById('document-list-body');
const searchInput = document.getElementById('search-input');
const sortSelect = document.getElementById('sort-select');
const typeFilter = document.getElementById('type-filter');
const selectAllCheckbox = document.getElementById('select-all');
const batchActionsDiv = document.getElementById('batch-actions');
const selectedCountSpan = document.getElementById('selected-count');
const paginationDiv = document.getElementById('pagination');
const pageInfo = document.getElementById('page-info');
const prevPageBtn = document.getElementById('prev-page');
const nextPageBtn = document.getElementById('next-page');
const pageSizeSelect = document.getElementById('page-size-select');
const totalCountDiv = document.getElementById('total-count');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDocuments();
    loadDocumentStats();
    
    // 搜索框回车事件
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchDocuments();
        }
    });
    
    // 实时搜索（防抖）
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(searchDocuments, 500);
    });
});

// 加载文档列表
async function loadDocuments() {
    try {
        showLoading();
        
        // 使用新的文档管理API
        const response = await fetch('/api/v2/documents/list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                page: currentPage,
                page_size: pageSize,
                sort_by: currentSort.field,
                sort_order: currentSort.order,
                filters: currentFilters
            })
        });
        
        if (!response.ok) {
            // 如果新API不可用，回退到旧API
            const fallbackResponse = await fetch('/api/v1/documents/');
            if (!fallbackResponse.ok) {
                throw new Error('获取文档列表失败');
            }
            const documents = await fallbackResponse.json();
            allDocuments = documents;
            filteredDocuments = documents;
            totalPages = Math.ceil(documents.length / pageSize);
        } else {
            const data = await response.json();
            allDocuments = data.documents;
            filteredDocuments = data.documents;
            totalPages = data.total_pages;
            currentPage = data.page;
        }
        
        renderDocumentList();
        updatePagination();
        updateTotalCount();
        
    } catch (error) {
        console.error('加载文档失败:', error);
        showError('加载文档失败: ' + error.message);
    }
}

// 加载文档统计
async function loadDocumentStats() {
    try {
        const response = await fetch('/api/v2/documents/stats');
        
        if (response.ok) {
            const stats = await response.json();
            updateStatsDisplay(stats);
        } else {
            // 如果新API不可用，使用基础统计
            const documents = await fetch('/api/v1/documents/').then(r => r.json());
            const basicStats = calculateBasicStats(documents);
            updateStatsDisplay(basicStats);
        }
        
    } catch (error) {
        console.error('加载统计信息失败:', error);
    }
}

// 计算基础统计信息
function calculateBasicStats(documents) {
    const total = documents.length;
    const totalWords = documents.reduce((sum, doc) => sum + (doc.word_count || 0), 0);
    const avgWords = total > 0 ? Math.round(totalWords / total) : 0;
    
    // 计算本周新增
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    const recentDocs = documents.filter(doc => {
        const createdAt = new Date(doc.created_at);
        return createdAt >= oneWeekAgo;
    }).length;
    
    return {
        total_documents: total,
        total_word_count: totalWords,
        avg_word_count: avgWords,
        recent_activity: [{ count: recentDocs }]
    };
}

// 更新统计显示
function updateStatsDisplay(stats) {
    document.getElementById('total-docs').textContent = stats.total_documents || 0;
    document.getElementById('total-words').textContent = formatNumber(stats.total_word_count || 0);
    document.getElementById('avg-words').textContent = Math.round(stats.avg_word_count || 0);
    
    // 本周新增文档数
    const recentCount = stats.recent_activity ? stats.recent_activity.length : 0;
    document.getElementById('recent-docs').textContent = recentCount;
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 渲染文档列表
function renderDocumentList() {
    if (filteredDocuments.length === 0) {
        documentListBody.innerHTML = `
            <div class="empty-state">
                <h3>📄 暂无文档</h3>
                <p>还没有上传任何文档，请先上传文档进行分析</p>
            </div>
        `;
        return;
    }
    
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filteredDocuments.length);
    const pageDocuments = filteredDocuments.slice(startIndex, endIndex);
    
    const html = pageDocuments.map(doc => {
        const isSelected = selectedDocuments.has(doc.doc_id);
        const createdDate = new Date(doc.created_at).toLocaleDateString('zh-CN');
        const docType = doc.metadata?.type || '未分类';
        
        return `
            <div class="document-item">
                <input type="checkbox" class="document-checkbox" 
                       ${isSelected ? 'checked' : ''} 
                       onchange="toggleDocumentSelection('${doc.doc_id}')">
                <div>
                    <div class="document-title" onclick="viewDocument('${doc.doc_id}')">${doc.title}</div>
                    <div class="document-meta">ID: ${doc.doc_id}</div>
                </div>
                <div class="document-date">${createdDate}</div>
                <div class="document-words">${formatNumber(doc.word_count || 0)}</div>
                <div class="document-type">${docType}</div>
                <div class="document-actions">
                    <button class="action-btn btn-view" onclick="viewDocument('${doc.doc_id}')" title="查看">👁️</button>
                    <button class="action-btn btn-edit" onclick="editDocument('${doc.doc_id}')" title="编辑">✏️</button>
                    <button class="action-btn btn-delete" onclick="deleteDocument('${doc.doc_id}')" title="删除">🗑️</button>
                </div>
            </div>
        `;
    }).join('');
    
    documentListBody.innerHTML = html;
    updateBatchActions();
}

// 显示加载状态
function showLoading() {
    documentListBody.innerHTML = `
        <div class="loading">
            <p>正在加载文档...</p>
        </div>
    `;
}

// 显示错误信息
function showError(message) {
    documentListBody.innerHTML = `
        <div class="empty-state">
            <h3>❌ 加载失败</h3>
            <p>${message}</p>
            <button class="btn-primary" onclick="loadDocuments()">重试</button>
        </div>
    `;
}

// 搜索文档
async function searchDocuments() {
    const query = searchInput.value.trim();
    
    if (!query) {
        filteredDocuments = allDocuments;
        currentPage = 1;
        renderDocumentList();
        updatePagination();
        updateTotalCount();
        return;
    }
    
    try {
        const response = await fetch('/api/v2/documents/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: query,
                search_in: ['title', 'content'],
                page: 1,
                page_size: 500, // 获取更多搜索结果
                highlight: true
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            filteredDocuments = data.results.map(result => result.document);
        } else {
            // 回退到客户端搜索
            const queryLower = query.toLowerCase();
            filteredDocuments = allDocuments.filter(doc => 
                doc.title.toLowerCase().includes(queryLower) ||
                doc.text.toLowerCase().includes(queryLower)
            );
        }
        
        currentPage = 1;
        totalPages = Math.ceil(filteredDocuments.length / pageSize);
        renderDocumentList();
        updatePagination();
        updateTotalCount();
        
    } catch (error) {
        console.error('搜索失败:', error);
        showMessage('搜索失败: ' + error.message, 'error');
    }
}

// 应用排序
function applySorting() {
    const sortValue = sortSelect.value;
    const [field, order] = sortValue.split('_');
    
    currentSort = { field, order };
    
    // 客户端排序
    filteredDocuments.sort((a, b) => {
        let aValue, bValue;
        
        switch (field) {
            case 'created':
                aValue = new Date(a.created_at);
                bValue = new Date(b.created_at);
                break;
            case 'title':
                aValue = a.title.toLowerCase();
                bValue = b.title.toLowerCase();
                break;
            case 'word':
                aValue = a.word_count || 0;
                bValue = b.word_count || 0;
                break;
            default:
                return 0;
        }
        
        if (order === 'desc') {
            return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
        } else {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
    });
    
    currentPage = 1;
    renderDocumentList();
    updatePagination();
    updateTotalCount();
}

// 应用筛选
function applyFilters() {
    const typeValue = typeFilter.value;
    
    if (!typeValue) {
        filteredDocuments = allDocuments;
    } else {
        filteredDocuments = allDocuments.filter(doc => {
            const docType = doc.metadata?.type || '未分类';
            return docType === typeValue;
        });
    }
    
    currentPage = 1;
    totalPages = Math.ceil(filteredDocuments.length / pageSize);
    renderDocumentList();
    updatePagination();
    updateTotalCount();
}

// 切换文档选择
function toggleDocumentSelection(docId) {
    if (selectedDocuments.has(docId)) {
        selectedDocuments.delete(docId);
    } else {
        selectedDocuments.add(docId);
    }
    updateBatchActions();
    updateSelectAllCheckbox();
}

// 全选/取消全选
function toggleSelectAll() {
    const isChecked = selectAllCheckbox.checked;
    
    if (isChecked) {
        // 选择当前页面的所有文档
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, filteredDocuments.length);
        const pageDocuments = filteredDocuments.slice(startIndex, endIndex);
        
        pageDocuments.forEach(doc => {
            selectedDocuments.add(doc.doc_id);
        });
    } else {
        selectedDocuments.clear();
    }
    
    renderDocumentList();
    updateBatchActions();
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filteredDocuments.length);
    const pageDocuments = filteredDocuments.slice(startIndex, endIndex);
    
    const selectedInPage = pageDocuments.filter(doc => selectedDocuments.has(doc.doc_id)).length;
    
    if (selectedInPage === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (selectedInPage === pageDocuments.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 更新批量操作区域
function updateBatchActions() {
    const selectedCount = selectedDocuments.size;
    
    if (selectedCount > 0) {
        batchActionsDiv.style.display = 'flex';
        selectedCountSpan.textContent = `已选择 ${selectedCount} 个文档`;
    } else {
        batchActionsDiv.style.display = 'none';
    }
}

// 更新分页
function updatePagination() {
    if (totalPages <= 1) {
        paginationDiv.style.display = 'none';
        return;
    }
    
    paginationDiv.style.display = 'flex';
    pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
    
    prevPageBtn.disabled = currentPage <= 1;
    nextPageBtn.disabled = currentPage >= totalPages;
}

// 上一页
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        renderDocumentList();
        updatePagination();
    }
}

// 下一页
function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        renderDocumentList();
        updatePagination();
    }
}

// 刷新文档列表
function refreshDocuments() {
    selectedDocuments.clear();
    currentPage = 1;
    loadDocuments();
    loadDocumentStats();
}

// 更改页面大小
function changePageSize() {
    pageSize = parseInt(pageSizeSelect.value);
    currentPage = 1;
    renderDocumentList();
    updatePagination();
    updateTotalCount();
}

// 更新总数显示
function updateTotalCount() {
    const total = filteredDocuments.length;
    const startIndex = (currentPage - 1) * pageSize + 1;
    const endIndex = Math.min(currentPage * pageSize, total);

    if (total === 0) {
        totalCountDiv.textContent = '共 0 个文档';
    } else {
        totalCountDiv.textContent = `显示 ${startIndex}-${endIndex} / 共 ${total} 个文档`;
    }
}

// 查看文档
async function viewDocument(docId) {
    try {
        // 获取文档详情
        const response = await fetch(`/api/v1/documents/${docId}`);
        if (!response.ok) {
            throw new Error('获取文档失败');
        }

        const document = await response.json();

        // 显示文档预览模态框
        showDocumentPreview(document);
    } catch (error) {
        console.error('查看文档失败:', error);
        alert('查看文档失败: ' + error.message);
    }
}

// 显示文档预览模态框
function showDocumentPreview(docData) {
    const modal = document.getElementById('preview-modal');
    const title = document.getElementById('preview-title');
    const content = document.getElementById('preview-content');
    const metadata = document.getElementById('preview-metadata');
    const analyzeBtn = document.getElementById('analyze-document-btn');

    // 设置标题
    title.textContent = docData.title || docData.doc_id;

    // 设置内容
    content.textContent = docData.content || t('documentManagement.noContent');

    // 设置元数据
    const createdDate = docData.created_at ? new Date(docData.created_at).toLocaleString() : t('documentManagement.unknown');
    const wordCount = docData.word_count || 0;
    const docType = getDocumentType(docData.doc_id);

    metadata.innerHTML = `
        <div class="metadata-item"><strong>${t('documentManagement.documentId')}:</strong> ${docData.doc_id}</div>
        <div class="metadata-item"><strong>${t('documentManagement.createdTime')}:</strong> ${createdDate}</div>
        <div class="metadata-item"><strong>${t('documentManagement.wordCount')}:</strong> ${formatNumber(wordCount)}</div>
        <div class="metadata-item"><strong>${t('documentManagement.documentType')}:</strong> ${docType}</div>
    `;

    // 设置分析按钮
    analyzeBtn.onclick = () => {
        sessionStorage.setItem('preloadDocId', docData.doc_id);
        window.location.href = '/';
    };

    // 显示模态框
    modal.style.display = 'flex';
}

// 获取文档类型
function getDocumentType(docId) {
    if (docId.includes('.pdf')) return t('documentManagement.pdfDocument');
    if (docId.includes('.doc') || docId.includes('.docx')) return t('documentManagement.wordDocument');
    if (docId.includes('.txt')) return t('documentManagement.textDocument');
    if (docId.includes('.md')) return t('documentManagement.markdownDocument');
    return t('documentManagement.unknownType');
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 编辑文档
function editDocument(docId) {
    showMessage('编辑功能开发中...', 'info');
}

// 删除文档
function deleteDocument(docId) {
    window.currentDeleteDocId = docId;
    document.getElementById('delete-message').textContent = '确定要删除这个文档吗？此操作不可撤销。';
    showModal('delete-modal');
}

// 确认删除
async function confirmDelete() {
    const docId = window.currentDeleteDocId;
    
    try {
        const response = await fetch(`/api/v1/documents/${docId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showMessage('文档删除成功', 'success');
            selectedDocuments.delete(docId);
            loadDocuments();
            loadDocumentStats();
        } else {
            throw new Error('删除失败');
        }
        
    } catch (error) {
        console.error('删除文档失败:', error);
        showMessage('删除文档失败: ' + error.message, 'error');
    }
    
    closeModal('delete-modal');
}

// 批量删除
async function batchDelete() {
    if (selectedDocuments.size === 0) {
        showMessage('请先选择要删除的文档', 'warning');
        return;
    }
    
    const count = selectedDocuments.size;
    document.getElementById('delete-message').textContent = `确定要删除选中的 ${count} 个文档吗？此操作不可撤销。`;
    
    window.currentDeleteDocId = 'batch';
    showModal('delete-modal');
}

// 批量导出
function batchExport() {
    if (selectedDocuments.size === 0) {
        showMessage('请先选择要导出的文档', 'warning');
        return;
    }
    
    showMessage('导出功能开发中...', 'info');
}

// 显示模态框
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
    }
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 1001;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    switch (type) {
        case 'success':
            messageDiv.style.background = 'var(--success-color)';
            break;
        case 'error':
            messageDiv.style.background = 'var(--error-color)';
            break;
        case 'warning':
            messageDiv.style.background = 'var(--warning-color)';
            break;
        default:
            messageDiv.style.background = 'var(--primary-color)';
    }
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
