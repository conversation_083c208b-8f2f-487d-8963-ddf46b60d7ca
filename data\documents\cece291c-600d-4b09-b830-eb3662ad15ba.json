{"doc_id": "cece291c-600d-4b09-b830-eb3662ad15ba", "title": "item256_<PERSON>_<PERSON><PERSON>, Ph.D., Assistant Professor of Political Science, George Washington University", "text": "  STATEMENT OF JEFFREY DING, PhD, ASSISTANT PROFESSOR OF \r\n        POLITICAL SCIENCE, <PERSON>ORGE WASHINGTON UNIVERSITY\r\n\r\n    Dr. Ding. Chairman <PERSON>, Vice Chairman <PERSON><PERSON><PERSON>, and Members \r\nof the Committee. I am honored by the opportunity to brief this \r\nCommittee on the National Security Implications of AI. In all \r\nhonesty, I also have a selfish reason for attending today. I \r\nteach political science at GW, and my students all really look \r\nup to the Committee Members in this room and also all the staff \r\nwho are working behind the scenes to put this hearing together. \r\nSo, when I got to tell the class this morning that I was doing \r\nthis testimony, they all got the most excited I've ever seen \r\nthem get excited this semester. And so, hopefully, that will \r\ncause them to do more of the required readings in class. In all \r\nseriousness, I have great students, I'm very grateful to be \r\nhere.\r\n    Today, in my opening remarks, I want to make three main \r\npoints from my written testimony. The first is when it comes to \r\nthe national security of implications of AI, the main driver \r\nand the main vector is which country will be able to sustain \r\nproductivity growth at higher levels than their rivals. And for \r\nthis vector, the distinction between innovation capacity and \r\ndiffusion capacity is central to thinking about technological \r\nleadership in AI. Today, when various groups--whether that be \r\nexperts, policymakers, the Intelligence Community--when they \r\ntry to assess technological leadership, they are overly \r\npreoccupied with innovation capacity. Which state is going to \r\nbe the first to generate new-to-the-world breakthroughs, the \r\nfirst to generate that next leap in large language models. They \r\nneglect diffusion capacity. A state's ability to spread and \r\nadopt innovations after their initial introduction across \r\nproductive processes.\r\n    And that process of diffusion throughout the entire economy \r\nis really important for technologies like AI. If we were \r\ntalking about a sector like automobiles, or even a sector like \r\nclean energy, we might not be talking as much about the effects \r\nof spreading technologies across all different productive \r\nprocesses throughout the entire economy. AI is a general-\r\npurpose technology, like electricity, like the computer, like \r\nmy fellow panelists just mentioned in his testimony. And \r\ngeneral-purpose technologies historically precede waves of \r\nproductivity growth because they can have pervasive effects \r\nthroughout the entire economy. So, the U.S. in the late 19th \r\ncentury became the leading economic power before it translated \r\nthat influence into military and geopolitical leadership, \r\nbecause it was better at adopting general purpose technologies \r\nat scale, like electricity, like the American system of \r\ninterchangeable manufacture, at a better and a more effective \r\nrate than its rivals.\r\n    Point number two is when we assess China's technological \r\nleadership and use this framework of innovation capacity versus \r\ndiffusion capacity, my research finds that China faces a \r\ndiffusion deficit. Its ability to diffuse innovations like AI \r\nacross the entire economy lags far behind its ability to \r\npioneer initial innovations or make fundamental breakthroughs.\r\n    And so, when you've heard from other people in the past or \r\nin the briefing memos you are reading, you are probably getting \r\na lot of innovation-centric indicators of China's scientific \r\nand technological prowess: its advances in R&D spending, \r\nheadline numbers on patents and publications. In my research, \r\nI've presented evidence about China's diffusion deficit by \r\nlooking at how is China actually adopting other information and \r\ncommunications technologies at scale? What are its adoption \r\nrates in cloud computing, industrial software, related \r\ntechnologies that would all be in a similar category to AI? And \r\nthose rates lag far behind the U.S.\r\n    Another indicator would be how is China's ability to widen \r\nthe pool of average AI engineers? I'm not talking about Nobel \r\nPrize of computing winners like my fellow panelists here, but \r\njust average AI engineers who can take existing models and \r\nadapt them in particular sectors or industries or specific \r\napplications. And based on my data, China has only 29 \r\nuniversities that meet a baseline quality metric for AI \r\nengineering, whereas the U.S. has 159. So, there's a large gap \r\nin terms of China's diffusion capacity compared to its \r\ninnovation capacity in AI.\r\n    I'll close with the third point, which is some recent \r\ntrends in Chinese labs' large language models. China has built \r\nlarge language models similar to OpenAI's ChatGPT, as well as \r\nOpenAI's text-to-image models like DALL-E. But there's still a \r\nlarge gap in terms of Chinese performance on these models. And, \r\nin fact, on benchmarks and leaderboards where U.S. models are \r\ncompared to Chinese models on Chinese language prompts, models \r\nlike ChatGPT still perform better than Chinese counterparts. \r\nSome of these bottlenecks relate to a reliance on Western \r\ncompanies to open up new paradigms, China's censorship regime, \r\nwhich Dr. Jensen talked about, and computing power bottlenecks, \r\nwhich I'm happy to expand on further.\r\n    I'll close by saying I submitted three specific policy \r\nrecommendations to the Committee. But I want to emphasize one, \r\nwhich is keep calm and avoid overhyping China's AI \r\ncapabilities. In the paper that forms the basis for this \r\ntestimony, I called attention to a 1969 CIA assessment of the \r\nSoviet Union's technological capabilities. It was remarkable \r\nbecause it went against the dominant narrative of the time of a \r\nSoviet Union close to overtaking the U.S. in technological \r\nleadership. The report concluded that the technological gap was \r\nactually widening between the U.S. as the leader and the Soviet \r\nUnion because of the U.S.'s superior mechanisms to spread \r\ntechnologies and diffuse technologies. Fifty years later, we \r\nknow why this assessment was right, and we know we have to \r\nfocus on diffusion capacity when it comes to scientific and \r\ntechnological leadership.\r\n    Thanks for your time.", "metadata": {"original_filename": "item256_<PERSON>_<PERSON><PERSON>, Ph.D., Assistant Professor of Political Science, George Washington University.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:21.082374", "updated_at": "2025-08-29T02:04:21.082374", "word_count": 6173}