#!/usr/bin/env python3
"""
测试高级分析API的可用性
"""

import requests
import json
import sys

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://127.0.0.1:8000"
    
    # 测试基本连接
    print("🔍 测试服务器连接...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ 服务器连接成功: {response.status_code}")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    # 测试高级分析能力端点
    print("\n🔍 测试高级分析能力端点...")
    try:
        response = requests.get(f"{base_url}/api/v2/analysis/capabilities")
        if response.status_code == 200:
            capabilities = response.json()
            print(f"✅ 高级分析能力端点正常，返回 {len(capabilities)} 个能力")
        else:
            print(f"❌ 高级分析能力端点失败: {response.status_code}")
            print(f"响应: {response.text}")
    except Exception as e:
        print(f"❌ 高级分析能力端点错误: {e}")
    
    # 测试批量高级分析端点
    print("\n🔍 测试批量高级分析端点...")
    try:
        test_data = {
            "requests": [],
            "concurrency": 3,
            "batch_name": "测试批次",
            "priority": 0
        }
        
        response = requests.post(
            f"{base_url}/api/v2/analysis/batch-advanced",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 批量高级分析端点正常")
            print(f"批次ID: {result.get('batch_id', 'N/A')}")
        else:
            print(f"❌ 批量高级分析端点失败: {response.status_code}")
            print(f"响应: {response.text}")
    except Exception as e:
        print(f"❌ 批量高级分析端点错误: {e}")
    
    # 测试其他API端点
    print("\n🔍 测试其他API端点...")
    endpoints = [
        "/api/v1/documents/",
        "/api/v1/analysis/",
        "/api/v1/batch/status/test"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            if response.status_code in [200, 404, 422]:  # 404和422是预期的，因为我们没有提供正确的参数
                print(f"✅ {endpoint}: {response.status_code}")
            else:
                print(f"❌ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始API测试...")
    test_api_endpoints()
    print("\n✅ API测试完成")
