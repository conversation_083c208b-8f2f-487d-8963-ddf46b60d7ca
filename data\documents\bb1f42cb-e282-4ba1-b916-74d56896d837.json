{"doc_id": "bb1f42cb-e282-4ba1-b916-74d56896d837", "title": "item250_US_<PERSON><PERSON> <PERSON>, Executive Director, Center for Security and Emerging Technology", "text": "Dr. <PERSON>, Executive Director, Center for Security and Emerging Technology\r\n\r\nThank you, Chairman <PERSON>, Ranking Member <PERSON> and everyone on the committee to be, have this opportunity to talk about how we can make AI better for our country. there are many actions Congress can do to support AI innovation, protect key technology for misuse and ensure customers or consumers are safe. I'd like to highlight three today. First, we need to get used to working with AI as a society and individually we need to learn what, what, when we can trust our AI teammates and when to question or ignore them. I think this takes a lot of training and time. Two, we need skilled people to build future AI systems and to increase AI literacy. Three, we need to keep a close eye on the policies that we do enact to may need to make sure that every policy is being action, is being monitored, and make sure it's actually doing what we think it's doing and update that as we need to.\r\n\r\nThis is especially true when we're facing peer innovators, especially in a rapidly changing area like artificial intelligence. China is such a peer innovator, but we need to remember they're not 10 feet tall and they have different priorities for their AI than we do. China's AI leadership is evident through aggressive use of state power and substantial research investments, making it a peer innovator for us and our allies never far ahead and never far behind either. China focuses on how AI can assist military decision making and mass surveillance to help maintain societal control. This is very unlike the United States. Thankfully, managing that controlled means they're not letting AI run around all willy-nilly. In fact, they deploying of, the deployment of large language models does not appear to be a priority for China's leadership. Precisely for that reason, we should not let the fear of China surpassing the US deter oversight of the AI industry and AI technology.\r\n\r\nInstead, the focus should be on developing methods that allow enforcement of AI risk and harm management and guiding the innovation advancement of AI technology. I'd like to return to my first three points and expand on them a little bit. Going back to my opening points. The first one was, we must get used to working with AI via effective human machine teaming, which is central to AI's evolution in my opinion in the next decade. Understanding what an AI system can and cannot do and should and shouldn't do and when to rely on them and when to avoid using them should guide our future innovation and also our training standards. One thing that keeps me up at night is when human partners trust machines when they shouldn't, and there's interesting examples of that. they fail to trust AI when they should or are manipulated by a system, and there's some instances of that.\r\n\r\nAlso. We've witnessed rapid AI advancements and the convergence between AI and other sectors promises widespread innovation in areas from medical imaging to manufacturing. Therefore, fostering AI literacy across the population is critical for economic competitiveness, but also, and I think even more importantly, it is essential for democratic governance. We cannot engage in a meaningful societal, societal debate about AI if we don't understand enough about it. This means an increasingly large fraction of the US citizens will encounter AI daily, so that's the second point. We need skilled people working at all levels. We need innovators from technical and non-technical backgrounds. We need to attract and retain diverse talent from across our nation and internationally and separately from those who are building the AI systems, these future and current ones. We need comprehensive AI training for the general population, K through 12 curricula certifications. There's a lot of good ideas there.\r\n\r\nAI literacy is the central key though, so what else can we do? I think we can promote better decision making by gathering information now that we need to make decisions. For example, tracking AI harms via incident reporting is a good way to learn where things are breaking, learning how to request key model and training data for oversight to make sure it's being used in important applications correctly. We don't know how to do that. encouraging and developing third party auditing and ecosystem and the red teaming ecosystem. Excellent. If we are going to license AI software, which is a common proposal we hear, we're probably going to need to update existing authorities for existing agencies, and we may need to create a new one, a new agency or organization. This new organization could check how AI is being used and overseen by existing agencies.\r\n\r\nIt could be the first to deal with problems directing those in need to the right solutions either in the government or private sector and fill gaps in sector specific agencies. My last point, and I see I'm going too long. We need to make sure our policies are monitored and effectively implemented. There's really great ideas in the House and Senate on how to increase the analytic capacity to do that. I look forward to this discussion because I think this is a persistent issue that's it's just not going to go away and CSAT has and I have dedicated our professional lives to this, so thank you so much.", "metadata": {"original_filename": "item250_US_<PERSON><PERSON> <PERSON>, Executive Director, Center for Security and Emerging Technology.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:21.035298", "updated_at": "2025-08-29T02:04:21.035298", "word_count": 5314}