<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="titles.tableView">数据表格 - 文档分析系统</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <style>
        /* 数据表格页面特定样式 */
        .data-table-container {
            max-width: 1600px;
        }

        .table-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }

        .table-header p {
            margin: 0;
            font-size: 1.1em;
        }

        .controls-panel {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .data-tables {
            display: grid;
            gap: 30px;
        }

        .table-section {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .table-section h3 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
            font-size: 1.5em;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }

        .data-table td {
            max-width: 300px;
            word-wrap: break-word;
        }

        .json-viewer {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            color: var(--text-primary);
        }

        .tag {
            display: inline-block;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
        }

        .tag.primary {
            background: var(--primary-color);
            color: white;
        }

        .tag.success {
            background: var(--success-color);
            color: white;
        }

        .tag.warning {
            background: var(--warning-color);
            color: white;
        }

        .tag.danger {
            background: var(--error-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1 data-i18n="titles.systemName">📄 智能文档分析系统</h1>
            <p class="subtitle" data-i18n="titles.subtitle">基于智谱AI的政策文档深度分析工具</p>
            <nav class="main-nav">
                <a href="/" class="nav-link">🏠 文档分析</a>
                <a href="/static/visualization.html" class="nav-link">📊 数据可视化</a>
                <a href="/static/data-table.html" class="nav-link active">📋 表格视图</a>
                <a href="/static/document-management.html" class="nav-link">📁 文档管理</a>
                <a href="/static/prompts.html" class="nav-link">🔧 提示词编辑器</a>
                <a href="/docs" class="nav-link">📚 API文档</a>
                <button class="language-toggle" onclick="toggleLanguage()" title="Switch Language">EN</button>
                <button class="theme-toggle" onclick="toggleTheme()" title="切换主题">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main class="data-table-container">
            <!-- 页面标题 -->
            <div class="table-header">
                <h2 data-i18n="table.title">📊 数据表格视图</h2>
                <p data-i18n="table.description">详细展示文档分析的原始数据和结构化信息</p>
            </div>

        <!-- 控制面板 -->
        <div class="controls-panel">
            <div class="control-row">
                <div class="control-group">
                    <label for="doc-select" data-i18n="visualization.selectDocument">选择文档:</label>
                    <select id="doc-select" onchange="onDocumentSelect()">
                        <option value="" data-i18n="analysis.selectDocument">-- 选择已有文档 --</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="doc-id" data-i18n="analysis.documentId">或手动输入文档ID:</label>
                    <input type="text" id="doc-id" data-i18n="analysis.documentId" placeholder="输入文档ID" value="">
                </div>
            </div>
            <div class="control-row">
                <div class="control-group">
                    <button class="btn btn-primary" onclick="loadTableData()" data-i18n="table.loadData">🔄 加载数据</button>
                    <button class="btn btn-secondary" onclick="exportTableData()" data-i18n="table.exportData">📥 导出数据</button>
                    <a href="/static/visualization.html" class="btn btn-info" data-i18n="table.returnToCharts">📈 返回图表视图</a>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="data-tables" id="tables-container">
            <div class="loading">
                <div class="spinner"></div>
                <p data-i18n="table.selectDocumentPrompt">请选择文档并加载数据</p>
            </div>
        </div>
    </div>

    <script>
        let currentData = null;

        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (newTheme === 'dark') {
                icon.textContent = '☀️';
            } else {
                icon.textContent = '🌙';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('表格页面加载完成');

            // 默认设置为英文
            switchLanguage('en');

            // 初始化主题
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon && savedTheme === 'dark') {
                icon.textContent = '☀️';
            }

            loadDocumentList();
        });

        async function loadDocumentList() {
            try {
                console.log('开始加载文档列表...');
                const response = await fetch('/api/v1/documents');
                if (response.ok) {
                    const documents = await response.json();
                    console.log('获取到文档:', documents.length, '个');
                    const select = document.getElementById('doc-select');

                    if (!select) {
                        console.error('找不到doc-select元素');
                        return;
                    }

                    select.innerHTML = '<option value="">-- 选择已有文档 --</option>';

                    documents.forEach(doc => {
                        const option = document.createElement('option');
                        option.value = doc.doc_id;
                        option.textContent = `${doc.title || doc.doc_id} (${doc.doc_id})`;
                        select.appendChild(option);
                    });
                    console.log('文档列表加载完成');
                } else {
                    console.error('获取文档列表失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载文档列表失败:', error);
            }
        }

        function onDocumentSelect() {
            const select = document.getElementById('doc-select');
            const docIdInput = document.getElementById('doc-id');
            
            if (select.value) {
                docIdInput.value = select.value;
            }
        }

        async function loadTableData() {
            const docId = document.getElementById('doc-id').value.trim();
            if (!docId) {
                alert(t('errors.noDocumentSelected'));
                return;
            }

            const container = document.getElementById('tables-container');
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>${t('table.loadingData')}</p>
                </div>
            `;

            try {
                const response = await fetch(`/api/v1/visualization/data/${docId}?lang=${currentLanguage}`);
                if (response.ok) {
                    currentData = await response.json();
                    renderTables(currentData);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                container.innerHTML = `
                    <div class="table-section">
                        <h3>❌ ${t('table.loadFailed')}</h3>
                        <p>${t('errors.dataLoadFailed')}: ${error.message}</p>
                    </div>
                `;
            }
        }

        function renderTables(data) {
            const container = document.getElementById('tables-container');
            let html = '';

            // 1. 基本信息表格
            html += `
                <div class="table-section">
                    <h3>${t('table.basicInfo')}</h3>
                    <table class="data-table">
                        <tr><th>${t('tableFields.field')}</th><th>${t('tableFields.value')}</th></tr>
                        <tr><td>${t('tableFields.documentId')}</td><td>${data.doc_id || 'N/A'}</td></tr>
                        <tr><td>${t('tableFields.title')}</td><td>${data.title || 'N/A'}</td></tr>
                        <tr><td>${t('tableFields.analysisDate')}</td><td>${data.analysis_date || 'N/A'}</td></tr>
                        <tr><td>${t('tableFields.taskTypes')}</td><td>${(data.task_types || []).map(t => `<span class="tag primary">${getTaskTypeName(t)}</span>`).join('')}</td></tr>
                        <tr><td>${t('tableFields.hasRealData')}</td><td>${data.has_real_data ? `<span class="tag success">${t('tableFields.yes')}</span>` : `<span class="tag warning">${t('tableFields.no')}</span>`}</td></tr>
                    </table>
                </div>
            `;

            // 2. 摘要信息表格
            if (data.summary) {
                html += `
                    <div class="table-section">
                        <h3>${t('table.analysisSummary')}</h3>
                        <table class="data-table">
                            <tr><th>${t('tableFields.field')}</th><th>${t('tableFields.value')}</th></tr>
                            <tr><td>${t('visualization.totalActors')}</td><td>${data.summary.total_actors || 0}</td></tr>
                            <tr><td>${t('visualization.totalRelations')}</td><td>${data.summary.total_relations || 0}</td></tr>
                            <tr><td>${t('visualization.complexityScore')}</td><td>${data.summary.complexity_score || 0}</td></tr>
                            <tr><td>${t('visualization.keyThemes')}</td><td>${(data.summary.key_themes || []).map(t => `<span class="tag">${t}</span>`).join('')}</td></tr>
                        </table>
                    </div>
                `;
            }

            // 3. 真实分析结果表格
            if (data.real_analysis_results) {
                html += renderAnalysisResults(data.real_analysis_results);
            }

            // 4. 图表配置表格
            if (data.charts) {
                html += renderChartsInfo(data.charts);
            }

            // 5. 原始JSON数据
            html += `
                <div class="table-section">
                    <h3>🔍 原始JSON数据</h3>
                    <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                </div>
            `;

            container.innerHTML = html;
        }

        function renderAnalysisResults(results) {
            let html = `
                <div class="table-section">
                    <h3>🧠 真实分析结果</h3>
            `;

            Object.keys(results).forEach(taskType => {
                const result = results[taskType];
                html += `
                    <h4>${getTaskTypeName(taskType)}</h4>
                    <table class="data-table">
                        <tr><th>字段</th><th>值</th></tr>
                        <tr><td>任务类型</td><td><span class="tag primary">${taskType}</span></td></tr>
                        <tr><td>状态</td><td>${result.status ? '<span class="tag success">成功</span>' : '<span class="tag danger">失败</span>'}</td></tr>
                        <tr><td>执行时间</td><td>${result.execution_time || 'N/A'}</td></tr>
                `;

                if (result.result) {
                    const resultData = result.result;

                    // 行为者关系分析 - 详细信息
                    if (taskType === 'actor_relation') {
                        if (resultData.actors) {
                            html += `<tr><td>行为者数量</td><td>${resultData.actors.length}</td></tr>`;
                            html += renderActorsTable(resultData.actors);
                        }
                        if (resultData.relations) {
                            html += `<tr><td>关系数量</td><td>${resultData.relations.length}</td></tr>`;
                            html += renderRelationsTable(resultData.relations);
                        }
                    }

                    // 角色塑造分析 - 详细信息
                    if (taskType === 'role_framing') {
                        if (resultData.heroes) {
                            html += `<tr><td>英雄数量</td><td>${resultData.heroes.length}</td></tr>`;
                            html += renderRolesTable('英雄', resultData.heroes);
                        }
                        if (resultData.victims) {
                            html += `<tr><td>受害者数量</td><td>${resultData.victims.length}</td></tr>`;
                            html += renderRolesTable('受害者', resultData.victims);
                        }
                        if (resultData.villains) {
                            html += `<tr><td>反派数量</td><td>${resultData.villains.length}</td></tr>`;
                            html += renderRolesTable('反派', resultData.villains);
                        }
                    }

                    // 问题范围分析 - 详细信息
                    if (taskType === 'problem_scope') {
                        if (resultData.expansion_strategies) {
                            html += `<tr><td>扩大化策略</td><td>${resultData.expansion_strategies.length}</td></tr>`;
                            html += renderStrategiesTable('扩大化策略', resultData.expansion_strategies);
                        }
                        if (resultData.reduction_strategies) {
                            html += `<tr><td>缩小化策略</td><td>${resultData.reduction_strategies.length}</td></tr>`;
                            html += renderStrategiesTable('缩小化策略', resultData.reduction_strategies);
                        }
                    }

                    // 因果机制分析 - 详细信息
                    if (taskType === 'causal_mechanism') {
                        if (resultData.causal_chains) {
                            html += `<tr><td>因果链数量</td><td>${resultData.causal_chains.length}</td></tr>`;
                            html += renderCausalChainsTable(resultData.causal_chains);
                        }
                        if (resultData.mechanisms) {
                            html += `<tr><td>机制数量</td><td>${resultData.mechanisms.length}</td></tr>`;
                            html += renderMechanismsTable(resultData.mechanisms);
                        }
                    }
                }

                html += `</table><br>`;
            });

            html += `</div>`;
            return html;
        }

        function renderChartsInfo(charts) {
            let html = `
                <div class="table-section">
                    <h3>📈 图表配置信息</h3>
                    <table class="data-table">
                        <thead>
                            <tr><th>任务类型</th><th>图表数量</th><th>图表类型</th><th>图表标题</th></tr>
                        </thead>
                        <tbody>
            `;

            Object.keys(charts).forEach(taskType => {
                const taskCharts = charts[taskType] || [];
                taskCharts.forEach((chart, index) => {
                    html += `
                        <tr>
                            <td><span class="tag primary">${getTaskTypeName(taskType)}</span></td>
                            <td>${index + 1}</td>
                            <td><span class="tag">${chart.type}</span></td>
                            <td>${chart.title || 'N/A'}</td>
                        </tr>
                    `;
                });
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;
            return html;
        }

        function getTaskTypeName(taskType) {
            const taskTypeKey = `taskTypes.${taskType.replace('_', '')}`;
            return t(taskTypeKey) || t(`taskTypes.${taskType}`) || taskType;
        }

        function exportTableData() {
            if (!currentData) {
                alert(t('table.noDataToExport'));
                return;
            }

            const dataStr = JSON.stringify(currentData, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analysis_data_${currentData.doc_id || 'unknown'}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 渲染行为者详细表格
        function renderActorsTable(actors) {
            if (!actors || actors.length === 0) return '';

            let html = `
                </table>
                <h5>📋 行为者详细信息</h5>
                <table class="data-table">
                    <thead>
                        <tr><th>名称</th><th>类型</th><th>描述</th><th>行动数量</th><th>主要行动</th></tr>
                    </thead>
                    <tbody>
            `;

            actors.slice(0, 10).forEach(actor => {
                const actions = actor.actions || [];
                const mainActions = actions.slice(0, 3).map(action =>
                    typeof action === 'string' ? action : (action.description || action.name || '未知')
                ).join(', ');

                html += `
                    <tr>
                        <td><strong>${actor.name || '未知'}</strong></td>
                        <td><span class="tag">${actor.type || '未知'}</span></td>
                        <td>${(actor.description || '无描述').substring(0, 100)}${(actor.description || '').length > 100 ? '...' : ''}</td>
                        <td>${actions.length}</td>
                        <td>${mainActions || '无行动'}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                <table class="data-table">
            `;
            return html;
        }

        // 渲染关系详细表格
        function renderRelationsTable(relations) {
            if (!relations || relations.length === 0) return '';

            let html = `
                </table>
                <h5>🔗 关系详细信息</h5>
                <table class="data-table">
                    <thead>
                        <tr><th>源</th><th>目标</th><th>关系类型</th><th>描述</th><th>强度</th></tr>
                    </thead>
                    <tbody>
            `;

            relations.slice(0, 10).forEach(relation => {
                html += `
                    <tr>
                        <td><strong>${relation.source || '未知'}</strong></td>
                        <td><strong>${relation.target || '未知'}</strong></td>
                        <td><span class="tag primary">${relation.type || '未知'}</span></td>
                        <td>${(relation.description || '无描述').substring(0, 80)}${(relation.description || '').length > 80 ? '...' : ''}</td>
                        <td>${relation.strength || 'N/A'}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                <table class="data-table">
            `;
            return html;
        }

        // 渲染角色详细表格
        function renderRolesTable(roleType, roles) {
            if (!roles || roles.length === 0) return '';

            let html = `
                </table>
                <h5>🎭 ${roleType}详细信息</h5>
                <table class="data-table">
                    <thead>
                        <tr><th>名称</th><th>描述</th><th>证据数量</th><th>主要证据</th></tr>
                    </thead>
                    <tbody>
            `;

            roles.slice(0, 8).forEach(role => {
                const evidence = role.evidence || [];
                const mainEvidence = evidence.slice(0, 2).join(', ');

                html += `
                    <tr>
                        <td><strong>${role.name || '未知'}</strong></td>
                        <td>${(role.description || '无描述').substring(0, 120)}${(role.description || '').length > 120 ? '...' : ''}</td>
                        <td>${evidence.length}</td>
                        <td>${mainEvidence || '无证据'}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                <table class="data-table">
            `;
            return html;
        }

        // 渲染策略详细表格
        function renderStrategiesTable(strategyType, strategies) {
            if (!strategies || strategies.length === 0) return '';

            let html = `
                </table>
                <h5>📋 ${strategyType}详细信息</h5>
                <table class="data-table">
                    <thead>
                        <tr><th>策略类型</th><th>描述</th><th>示例数量</th><th>主要示例</th></tr>
                    </thead>
                    <tbody>
            `;

            strategies.slice(0, 8).forEach(strategy => {
                const examples = strategy.examples || [];
                const mainExamples = examples.slice(0, 2).join(', ');

                html += `
                    <tr>
                        <td><span class="tag warning">${strategy.type || '未知'}</span></td>
                        <td>${(strategy.description || '无描述').substring(0, 100)}${(strategy.description || '').length > 100 ? '...' : ''}</td>
                        <td>${examples.length}</td>
                        <td>${mainExamples || '无示例'}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                <table class="data-table">
            `;
            return html;
        }

        // 渲染因果链详细表格
        function renderCausalChainsTable(chains) {
            if (!chains || chains.length === 0) return '';

            let html = `
                </table>
                <h5>🔗 因果链详细信息</h5>
                <table class="data-table">
                    <thead>
                        <tr><th>链ID</th><th>步骤数</th><th>条件数</th><th>主要步骤</th><th>描述</th></tr>
                    </thead>
                    <tbody>
            `;

            chains.slice(0, 8).forEach((chain, index) => {
                const steps = chain.steps || [];
                const conditions = chain.conditions || [];
                const mainSteps = steps.slice(0, 3).map(step =>
                    typeof step === 'string' ? step : (step.description || step.name || '未知步骤')
                ).join(' → ');

                html += `
                    <tr>
                        <td><strong>链 ${index + 1}</strong></td>
                        <td>${steps.length}</td>
                        <td>${conditions.length}</td>
                        <td>${mainSteps || '无步骤'}</td>
                        <td>${(chain.description || '无描述').substring(0, 80)}${(chain.description || '').length > 80 ? '...' : ''}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                <table class="data-table">
            `;
            return html;
        }

        // 渲染机制详细表格
        function renderMechanismsTable(mechanisms) {
            if (!mechanisms || mechanisms.length === 0) return '';

            let html = `
                </table>
                <h5>⚙️ 机制详细信息</h5>
                <table class="data-table">
                    <thead>
                        <tr><th>机制类型</th><th>名称</th><th>描述</th><th>影响因子</th></tr>
                    </thead>
                    <tbody>
            `;

            mechanisms.slice(0, 8).forEach(mechanism => {
                const factors = mechanism.factors || mechanism.influences || [];
                const mainFactors = factors.slice(0, 3).join(', ');

                html += `
                    <tr>
                        <td><span class="tag danger">${mechanism.type || '未知'}</span></td>
                        <td><strong>${mechanism.name || '未知机制'}</strong></td>
                        <td>${(mechanism.description || '无描述').substring(0, 100)}${(mechanism.description || '').length > 100 ? '...' : ''}</td>
                        <td>${mainFactors || '无影响因子'}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;
            return html;
        }
    </script>
</body>
</html>
