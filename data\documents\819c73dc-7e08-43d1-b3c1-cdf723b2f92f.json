{"doc_id": "819c73dc-7e08-43d1-b3c1-cdf723b2f92f", "title": "item204_US_ITIs Global AI Policy Recommendations", "text": "\"ITI’s Global AI Policy\r\nRecommendations\"\r\n\r\nInvestment in AI\r\nInnovation and investment in AI will be key to\r\nfacilitating the development and uptake of AI\r\napplications. A robust strategy that supports\r\nmultiple components of innovation and\r\ninvestment will be necessary to harness the\r\ntechnology in a way that benefits many groups\r\nacross society. Governments should consider\r\nactions that guarantee a skilled workforce, utilize\r\npublicly available data, and support innovation.\r\nIn order to facilitate such a climate,\r\ngovernments should:\r\nSupport policies that will help\r\nto develop a skilled and diverse\r\nAI workforce.\r\nThese policies could include modernizing\r\ncandidate recruitment, hiring, and training, and\r\nshould establish and advance industry-informed\r\nskilling and re-skilling programs to prepare\r\nindividuals for the future of work, including an\r\nAI-enabled future. To support these initiatives,\r\nwe encourage governments and businesses to\r\ncontinue to focus on policies designed to advance\r\nand incentivize professional and technical\r\napprenticeships, education and training programs\r\nin STEM fields, and access to external and online\r\nreskilling programs. That said, AI is not just a\r\nfunction of STEM or advanced technical training;\r\nthe best way to ensure access to an AI workforce\r\nis to invest broadly across all relevant disciplines\r\nand teach flexible skills and problem solving\r\nfrom early childhood education. At the university\r\nlevel, AI and/or data science programs should\r\nincorporate the social sciences, humanities, and\r\nhistory to integrate humanistic approaches into\r\nthe curriculum beyond a single, separated “AI\r\nethics” unit.\r\nInvest in R&D for AI including\r\nbasic science.\r\nWe encourage robust government support\r\nfor research and development (R&D) to foster\r\ninnovation in AI through R&D incentives,\r\nand increased government funding of both\r\nfoundational basic science research and AIspecific\r\nresearch programs. As the primary source\r\nof funding for long-term, high-risk research\r\ninitiatives, we support governments’ investment\r\nin research fields specific or highly relevant to AI,\r\nincluding cyber-defense, data analytics, detection\r\nof fraudulent transactions or messages, adversarial\r\nmachine learning/AI (how to secure ML/AI), privacy\r\npreserving machine learning (PPML), robotics,\r\nhuman augmentation, natural language processing,\r\ninterfaces, and visualizations.\r\nIncrease access to government\r\nsources of publicly available data, as\r\nappropriate, in machine-readable formats\r\nand across borders to enable access to a\r\nfoundational building block of AI.\r\nData is fundamental to innovation in AI. It is\r\nimportant that data is high-quality, credible,\r\ntimely and available in machine-readable formats.\r\nBy leveraging large and diverse datasets and\r\nincreased computing power and ingenuity, AI\r\ndevelopers and other stakeholders will be able to\r\ninnovate and find solutions to meet the needs of\r\nindividuals and society in unprecedented ways.2\r\nMore available data means more data with which\r\nto train algorithms, resulting in higher quality AI\r\nofferings. Governments can also promote existing\r\ninternational standards regarding data, and data\r\nquality, and promote the development of new\r\nstandards for data quality. In addition to making\r\ndata available, governments may be able to\r\ncurate widely available data as labeled, diverse,\r\nrepresentative, quality data for the purposes of\r\ntraining corresponding AI.\r\n\r\nEnhance transparency of AI-enabled\r\nICT procurements across government\r\nagencies and ministries.\r\nTransparency in AI-enabled ICT procurements\r\ncan provide one metric to identify AI champions\r\nwithin government. Laggers may not know how\r\nbest to incorporate AI and would benefit from\r\nthe sharing of best practices and use-cases. For\r\nexample, adoption leaders could host inter-agency\r\nfora to discuss challenges and successes or put\r\ntogether senior level briefings to provide other\r\nagencies with a model roadmap for AI adoption.\r\nAdditionally, the aggregated information provides\r\ninsights on how to allocate or redistribute\r\navailable resources more efficiently to promote\r\nthe adoption of AI technologies.\r\nPrioritize procurement of AI-based\r\ntechnologies and applications as part\r\nof IT modernization efforts.\r\nThe deployment of AI tools will help governments\r\nat all levels leverage data generated by the\r\npublic sector as a strategic asset and make more\r\ninformed decisions about the actions that would\r\nbest serve constituents and ensure mission\r\nsuccess. Governments should upgrade legacy\r\nsystems and make ample investments in AI and\r\nsimilar technologies like robotic processing\r\nautomation (RPA). Modern cyberthreats are\r\nincreasingly automated, and machine learningbased\r\nprevention technologies are developing\r\nto increasingly leverage AI; investing in these\r\nsophisticated cybersecurity technologies should\r\nbe a priority as well.\r\nFacilitating Public Understanding\r\nof and Trust in AI\r\nOne of the most important ways governments can\r\nfoster the adoption of AI technology is to facilitate\r\npublic understanding and trust. Governments\r\nglobally can play a vital role in facilitating\r\ndialogues about AI between companies that use\r\nand develop AI and the communities they impact,\r\nwith the intent of better aligning them. As these\r\nstakeholders become more closely aligned,\r\ntrust will grow, and AI adoption will scale. To\r\ngrow public understanding of and trust in AI,\r\ngovernments should:\r\nPartner with or fund university\r\nprograms whereby data science\r\nand other students in aligned disciplines\r\nconduct real world projects with\r\ncommunities in key areas of social need..\r\nThis can significantly improve students’ skills while\r\nalso providing a tangible benefit to social groups\r\nin need. It also serves a training function for the\r\ncommunities involved, who learn what problems\r\nAI can and cannot solve, and how to make the\r\ntechnology work for them in a beneficial way. Some\r\nof those community members will also develop an\r\ninterest in AI and might go on to work in the field.\r\nSometimes recent STEM graduates may believe\r\nthat data science is merely technical, whereas a\r\nreally a portion of the job is in fact understanding\r\nthe problem domain and the stakeholders involved\r\nin order to translate their needs into data formats.\r\nA program along these lines would solve that\r\nproblem while also building public trust.\r\n\r\nConsider how to best promote\r\nthe development of meaningfully\r\nexplainable3 AI systems.\r\nExplainability is important for developing\r\naccountable and trustworthy AI because it\r\nenables the next step, agency—enabling entities\r\nto make decisions to avoid negative outcomes.\r\nTogether, explainability and agency can foster\r\naccountability and increase public trust. Several\r\nrecommendations may assist policymakers in\r\nthis effort:\r\n• Invest in research for and promote tools to\r\nachieve appropriate levels of explainability\r\nand pursue avenues to work with local\r\nresearch institutions, industry, and international\r\npartners in developing a common lexicon for\r\ntrustworthy AI.\r\n• Take a risk-based approach to explainability,\r\nconsidering where explainability makes\r\nsense in the AI space and where it might not.\r\nExplainability, while helpful in certain cases,\r\ndoes not make sense in every instance and\r\ncould serve to hamstring innovation. Some\r\nlow-risk applications of AI, for example, may\r\nnot necessitate the same type of explanations\r\nas higher-risk applications; and in more benign\r\napplications that carry non-significant impacts\r\non individuals, explanations may not be\r\nnecessary at all.\r\n• Explainability must also be balanced in\r\nconsideration of other factors, including the\r\nrights of individuals to receive an explanation,\r\nthe interests of businesses to maintain trade\r\nsecrets, and the potential value of exposed\r\ndata to potential adversaries. In considering\r\nAI explanations, value to the consumer is key\r\n– one of the benefits of AI explanations is to\r\nhelp individuals understand how the use of AI\r\nwill benefit them. It is also important to strike\r\na balance so that consumers do not experience\r\n“decision fatigue” and can understand the use of\r\nthe AI technology without being bogged down\r\nin technical details. Conversely, keeping some\r\ninformation related to AI systems obscured is\r\nimportant to protect businesses’ intellectual\r\nproperty interests, as well as the security of AI\r\nsystems more broadly.\r\n• Policymakers should avoid governance that\r\ncreates an environment where outliers are\r\nviewed as a flaw in an overall AI system. If an\r\noutlier is indeed an outlier, then the algorithm\r\nwill learn and dismiss it in later iterations so\r\nno “explanation” is necessary. As such, when\r\nand how an “explanation” may be required is\r\nhighly contingent on the stage of an AI system’s\r\ndevelopmental lifecycle, the context in which\r\na later-stage model is deployed, the purposes\r\nfor which it is deployed, and numerous other\r\nfactors. Any guidelines related to transparency\r\nor explainability should capture a statistically\r\nmeaningful number of results to ensure\r\nuncertain results are actual concerns and not just\r\nisolated anomalies.\r\nRely on conformity assessment\r\nonly in conjunction with other tools\r\nand after a comprehensive evaluation\r\nof whether a regulatory approach\r\nis warranted.\r\nGovernments are understandably considering\r\nwhether conformity assessment systems can\r\nplay a role in helping to generate confidence\r\nin AI systems. Conformity assessment systems\r\ninclude a suite of tools that provide for a range\r\nof approaches that can be calibrated to the level\r\nof risk associated with an AI system. However,\r\nwhether the use of conformity assessment is\r\nappropriate in the context of AI systems should be\r\ncarefully considered, and at a minimum requires\r\nidentifying whether standards exist that can\r\nbe used for conducting conformity assessment\r\nrelated activities such as certification of products\r\nor systems. Governments should be mindful in their use of conformance approaches to ensure\r\nthat they do not prescribe or mandate the use of\r\napproaches that can generate a misplaced sense of\r\nsecurity or lead to increased costs for customers.\r\nIn rapidly evolving systems such as AI systems, the\r\nuse of conformity assessment must be tailored in\r\na manner that factors in the constantly evolving\r\nnature of AI systems. A static approach where an\r\nAI system or product is tested or certified at the\r\noutset may provide little assurance about the\r\nproduct’s operation after it has been in use. In any\r\ncase, any conformity assessment scheme should\r\nbe developed within already existing sectorial\r\nlegislation frameworks (e.g., medical devices,\r\nmotor vehicles).\r\nEncourage an ethical\r\ndesign approach.\r\nIn designing AI systems, governments should\r\nencourage an approach that promotes fairness\r\nand non-discrimination. One set of perspectives\r\nthat may be worth considering are the Guidelines\r\ndeveloped by the European High-Level Expert\r\nGroup (HLEG Guidelines), which set forth seven\r\nfoundational principles that characterize a\r\ntrustworthy AI system. These principles include\r\nhuman agency and oversight, transparency,\r\nrobustness and safety, privacy and data\r\ngovernance, diversity, non-discrimination and\r\nfairness, societal and environmental well-being\r\nand accountability.4 However, since not all AI\r\napplications raise ethical questions as considered\r\nin the HLEG Guidelines, considerations of this type\r\nshould be context-specific and limited to high-risk\r\napplications. We expand on some key elements of\r\nthe HLEG Guidelines below:\r\n• Not all AI applications require the same level\r\nof human agency, and while for some\r\napplications it is very important (e.g., uses\r\nin aviation), it is not needed for others (e.g.,\r\nbaggage handling systems). Thus, when\r\ndetermining the degree of human involvement\r\nand oversight needed, individual use cases\r\nshould be taken into account.\r\n• Context and risk-level of different AI applications\r\nvary in terms of their impact on fundamental\r\nrights. For instance, the use of AI for automated\r\nbaggage handling in airports poses no risk to\r\nfundamental human rights,5 as opposed to\r\napplications in the field of HR.\r\n• We recommend discerning between\r\nunderstandability and interpretability.\r\nUnderstandability enables a non-technical\r\nperson (e.g., business executive or customer) to\r\ngain insight into how an algorithm works and\r\nwhy it made a given decision. Interpretability\r\nallows a technical expert, such as an AI/machine\r\nlearning expert, to understand why an algorithm\r\nmade a given decision. Both understandability\r\nand interpretability are key components of an\r\nethical design of AI. The distinction is necessary\r\nbecause the technical details of an AI system are\r\nnot necessarily meaningful or beneficial for the\r\nend-user.\r\n• Where possible, techniques such as\r\nanonymization, pseudonymization, deidentification\r\nand other privacy enhancing\r\ntechniques (PETs) and Privacy Preserving\r\nMachine Learning (PPML) are crucial to ensure\r\ndata can be used to train algorithms and perform\r\nAI tasks without breaching privacy. Users of AI\r\ncan leverage “federated learning” which means\r\nthey can aggregate data in ways so that the\r\nindividual data points are completely private,\r\nbut AI can be performed on the aggregate with\r\nminimal loss of accuracy.\r\nEnsuring the Security &\r\nPrivacy of AI Systems\r\nCybersecurity and privacy are foundational to\r\ntrustworthy AI systems, and there are multiple\r\nways in which AI, cybersecurity, and privacy\r\ninteract. First, AI is becoming increasingly\r\nessential to cybersecurity deterrence capabilities.\r\nWe discuss this further in Annex A, where we\r\noutline a cybersecurity use case for AI. Second,\r\nit is important that policymakers consider how\r\nto ensure the cybersecurity of AI systems. Third,\r\nusers must trust that their personal and sensitive\r\ndata used by AI systems is protected and\r\nhandled appropriately.\r\nGovernment policymakers should:\r\nEnsure that policies support the use\r\nof AI for cybersecurity purposes.\r\nCybersecurity tools and technologies should\r\nincorporate AI to keep pace with the evolving\r\nthreat landscape, including attackers who\r\nare constantly improving their sophisticated\r\nand highly automated methods to penetrate\r\norganizations and evade detection. Defensive\r\ncybersecurity technology can use machine\r\nlearning and AI to more effectively address today’s\r\nautomated, complex, and constantly evolving\r\ncyberattacks. When combined with cloud, AI\r\ncan help to scale cyber efforts through smart\r\nautomation and continuous learning that drives\r\nself-healing systems. To support and enable the\r\nuse of AI for cybersecurity purposes, policymakers\r\nmust carefully shape (or reaffirm)6 any policies\r\nrelated to privacy to affirmatively allow the use\r\nof personal information such as IP addresses to\r\nidentify malicious activity.\r\nEncourage public and private sector\r\nstakeholders to incorporate AI\r\nsystems into threat modeling and security\r\nrisk management.\r\nThis should include encouraging organizations\r\nto ensure that AI applications and related\r\nsystems are in scope for organizational security\r\nprogram monitoring and testing and that the\r\nrisk management implications of AI systems as a\r\npotential attack surface are considered.\r\nEncourage the use of strong, globally\r\naccepted and deployed cryptography\r\nand other security standards that enable\r\ntrust and interoperability in AI systems.\r\nThe tech sector incorporates strong security\r\nfeatures into our products and services to advance\r\ntrust, including AI systems. Policymakers should\r\npromote policies that support using published\r\nalgorithms as the default cryptography approach\r\nas they have the greatest trust among global\r\nstakeholders, and limit access to encryption keys.\r\nInvest in security innovation to\r\ncounter adversarial AII.\r\nIt is important that businesses and governments\r\nalso invest in cybersecurity directed at countering\r\nadversarial AI. For example, malicious actors can use\r\nadversarial AI to cause machine learning models to\r\nmisinterpret inputs into the system and behave in a\r\nway that is favorable to the attacker. To produce the\r\nunexpected behavior, attackers create “adversarial\r\nexamples” that often resemble normal inputs, but\r\ninstead are meticulously optimized to break the\r\nmodel’s performance. Adversarial AI represents an\r\nincremental threat compared to traditional cyberattacks,\r\nso it important that governments ensure\r\ntheir policy instruments do not inadvertently\r\nDevelop and support frameworks\r\nand guidelines that protect privacy\r\nand promote the appropriate/ethical\r\nuse of data that may be used in data sets\r\nunderpinning AI.\r\nTo protect personal information and support\r\nfundamental human rights, data in data sets used\r\nby AI systems may be required to be anonymized,\r\naggregated, or otherwise de-identified such that\r\nthe datasets exclude any personal information\r\nand cannot be re-identified. Doing so ensures the\r\nbeneficial use of the data in training intelligent\r\nsystems while protecting individual privacy and\r\nsecurity consistent with protecting fundamental\r\nhuman rights.\r\nApproaches to Regulation\r\nWe recommend that when governments consider\r\nregulating AI, they do so in a way that focuses on\r\nresponding effectively to specific harms while\r\nallowing for advancements in technology and\r\ninnovation. In doing so, governments should\r\nfully evaluate regulatory and non-regulatory\r\napproaches and only proceed to regulatory\r\napproaches when absolutely necessary. Regulation\r\nshould be design-neutral and risk-based. In\r\ntaking such an approach, governments can help\r\nensure that their policy levers address actual\r\ndemonstrated needs and are narrowly tailored,\r\nand do not inadvertently capture unrelated AI\r\nuses. In developing regulatory approaches to AI,\r\nwe offer the following recommendations:\r\nAlign around common parameters\r\nand consider the scope of AI.\r\nWhile there is not currently a universally accepted\r\ndefinition of AI, policymakers should strive to\r\ncoalesce around parameters of what constitutes\r\nan AI system to advance globally consistent AI\r\npolicy approaches. Appendix A compiles several\r\nkey AI terms around which international consensus\r\nis emerging, including AI systems, machine\r\nlearning, etc. Looking beyond the various relevant\r\ndefinitions, the scope of AI is incredibly broad and\r\ncould theoretically capture many different types of\r\nsystems and processes – so carefully articulating\r\nthe scope of AI implicated by a regulation is\r\nessential to establishing an informed baseline for\r\nAI policymaking.\r\nAn essential factor is to properly identify the\r\ncomponent parts of AI systems beyond algorithms\r\n(such as datasets and computing power), as well\r\nas to define related key terms such as machine\r\nlearning. Some algorithms have been applied\r\nfor decades but do not constitute “artificial\r\nintelligence” or “machine learning” systems. In\r\ncrafting any sort of incremental AI regulation,\r\npolicymakers must be clear on what aspect of AI\r\nthey are referring to and in what context. There\r\nis a difference between the latest wave of AI\r\nsystems that learn from data and experience,\r\nand traditional software and control systems that\r\noperate according to predictable rules, which have\r\nlong been embedded in a wide variety of highrisk\r\nsystems, from flight control to pacemakers.\r\nIn crafting any sort of incremental AI regulation,\r\npolicymakers must be clear on what aspect of AI\r\nthey are referring to and in what context.\r\nTake a risk-based, context-specific\r\napproach to governing AI.\r\nRisks need to be identified and mitigated in\r\nthe context of the specific AI use. This will help\r\npolicymakers determine use cases or applications\r\nthat are of particular concern, avoiding overly\r\nprescriptive approaches that may serve to stifle\r\ninnovation. Beyond that, and as we reference in\r\nour Facilitating Public Trust in AI section above,\r\ncontext is key. Not all AI applications negatively\r\nimpact humans and thus We recommend that policymakers, in close\r\nconsultation with industry and other stakeholders,\r\nconsider how to characterize “high-risk”\r\napplications of AI, including by identifying the\r\nappropriate roles for AI developers and users in\r\nmaking risk determinations. In our view, an AI\r\ndecision is high-risk when a negative outcome\r\ncould have a significant impact on people—\r\nespecially as it pertains to health, safety, freedom,\r\ndiscrimination, or human rights. In thinking about\r\nhigh-risk applications, focusing on “sectors”\r\nmay lead to overly broad categorizations – it is\r\nimportant to use a sufficiently targeted and welloutlined\r\nclassification to ensure this criterion does\r\nnot become irrelevant. We encourage developing\r\na categorization that takes into account sector, use\r\ncase, complexity of the AI system, probability of\r\nworst-case occurrence, irreversibility and scope\r\nof harm in worst-case scenarios e.g., individual v.\r\nlarger groups of people, and other criteria.\r\nA risk-based, context-specific approach will be the\r\nmost effective means of addressing concerns that\r\nmay be associated with AI, while simultaneously\r\nallowing for innovation and agility in development\r\nof AI applications. The AI development process is\r\nfast-evolving, highly varied between organizations,\r\nand geographically and technologically diffuse.\r\nAI models themselves have the potential to be\r\ncomplex and highly commercially sensitive. These\r\nfactors combine to suggest that an extensive,\r\nprescriptive, ‘one size fits all’ approach to AI\r\ngovernance will face similar, if not greater,\r\nchallenges than in other areas of technology\r\npolicy. This challenge is also manifest in the very\r\ndiverse application of AI and machine learning\r\n(ML) solutions which vary in sensitivity, risk\r\nand benefit.\r\nEvaluate existing laws and regulations\r\noverlapping with or adjacent to various\r\naspects of AI in order to determine whether\r\nthere are gaps requiring incremental new\r\nrules for AI.\r\nBecause AI is a horizontal technology, one should\r\nevaluate its use and impact in specific applications\r\nand evaluate whether existing rules, governing\r\nareas such as data protection/privacy or product\r\nliability already address possible emerging\r\nconcerns - rather than developing technologyspecific\r\nlaws. AI specific laws would run counter to\r\nthe principle of technological neutrality and such\r\nbroad regulation would likely become obsolete and\r\npossibly disproportionate to addressing identified\r\nrisks related to certain applications as technology\r\nand use cases evolve. Instead, governments should\r\nwork with industry and other AI stakeholders to\r\nfocus new rules on the use of technology in order to\r\naddress the potential issues arising in specific uses\r\nand applications.\r\nWhen conducting an evaluation, governments\r\nshould first identify what laws impact the use\r\ncases they are concerned with and then proceed\r\nto evaluate whether new rules are needed. They\r\nshould also avoid potential conflicts of law.\r\nPolicymakers should seek to ensure that any existing\r\nor forthcoming regulatory requirements do not\r\ncreate unnecessary technical barriers to trade and\r\nrely on global, industry-driven, voluntary, consensusbased\r\nstandards. Prior to drafting legislation or\r\nregulations, it is important that policymakers\r\nconsider how relevant, established and/or\r\ndeveloping international standards can inform the\r\ndevelopment of effective laws and regulations.\r\nPerhaps even more important than identifying and\r\naddressing gaps is clarifying how existing laws\r\napply to AI and how AI can be used in compliance\r\nwith existing laws. Many customers, particular in\r\nhighly regulated industries, are hesitant to use AI\r\nservices, because there is little certainty as to how\r\nsuch services can/should be used in compliance\r\nwith existing law. Granular, technical guidance or\r\ninstruction in this regard would be very helpful for\r\ngiving these types of customers confidence to use\r\nAI services\r\nGlobal Engagement\r\nAs countries around the world seek deploy and\r\nutilize AI, collaboration and conversation is key to\r\nensuring approaches are interoperable and aligned\r\nto the extent possible. The potential for regulatory\r\ndivergence is great, especially if countries\r\nundertake the development and deployment of AI\r\nin a vacuum. Thus, we recommend governments:\r\nRecognize the need for global\r\ncooperation and coordination.\r\nNations around the world seek to establish trust\r\nin AI applications. The OECD made important\r\nprogress in establishing AI principles of\r\ntrustworthiness and in ensuring that AI remains\r\nhuman-centered consistent with fundamental\r\n(democratic) values. These principles provide a\r\nconstructive policy blueprint, and we encourage\r\npolicymakers globally to look to them when\r\nconsidering how to approach AI to maintain\r\nalignment.7 We also encourage nations to join\r\nthe Global Partnership on AI (GPAI), which is an\r\nimportant vehicle for international collaboration.\r\nThe Group is currently exploring issues related\r\nto responsible AI, data governance, the future of\r\nwork, and innovation and commercialization.8\r\nSeek to maintain interoperability\r\nacross borders by engaging in\r\nongoing dialogue.\r\nGovernments should seek to maintain global\r\ninteroperability and alignment of various AI\r\nframeworks around the OECD AI principles\r\nreferenced above to the greatest extent possible.\r\nIn this era of global digital commerce, political\r\nand regulatory divergence poses real risks to the\r\nsocio-economic benefits and opportunities of\r\ndata-driven technologies such as AI, where fair,\r\naccurate, fit-for-purpose models depend on access\r\nto large, diverse data sets that can flow across\r\nborders. We believe that international dialogue\r\nwill spur wider dissemination of best practices,\r\ninformation, and guidance, increasing the\r\nlikelihood that policy and regulatory approaches\r\nare interoperable.\r\nEnsure the protected free flow of data\r\nacross borders.\r\nTo fully realize the benefits of AI, governments\r\nneed to ensure that data and meta-data can flow\r\nfreely and protected across borders. Data is and\r\nwill continue to be foundational to AI. As such,\r\nwe encourage governments to strengthen their\r\ncommitment to facilitating the free and protected\r\nflow of data across borders and refrain from\r\nimposing localization measures.\r\nSupport global, voluntary, industryled\r\nstandards.\r\nAI standards are essential to increase\r\ninteroperability, harmonization, and trust in\r\nAl systems. They can inform AI regulation,\r\npractical implementation, governance and\r\ntechnical requirements. Governments should\r\nwork to support global, voluntary, industryled\r\nstandards, and safeguard the work and\r\nprocesses of international standards development\r\nbodies. Broad contributions to and adoption of\r\ninternational standards reduces market access\r\nbarriers. Standards work for the net benefit of the\r\ninternational community and should be developed and applied without prejudice to cultural norms\r\nand without imposing the culture of any one\r\nnation. Standards work should also be technology\r\nneutral (avoiding preferential treatment for any\r\nspecific technical approach).\r\nFor example, ISO/IEC JTC 1/SC 42 on Artificial\r\nIntelligence has started working on Artificial\r\nIntelligence Management System (AIMS) standard\r\nthat will cover processes with development or use\r\nof Al, such as bias, fairness, inclusiveness, safety,\r\nsecurity, privacy, accountability, explicability,\r\nand transparency. This management system\r\nstandard will help in innovation and technology\r\ndevelopment through structured governance and\r\nappropriate risk management. SC 42 currently also\r\nhas other standards under development, focused\r\nvariously on terminology, reference architecture,\r\ngovernance of AI, and trustworthiness.", "metadata": {"original_filename": "item204_US_ITIs Global AI Policy Recommendations.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:20.644899", "updated_at": "2025-08-29T02:04:20.644899", "word_count": 27770}