{"doc_id": "52fc9fa9-0932-4a10-8ac3-b3d3ffcafada", "title": "item041_US_NVIDIA Lends Support to Washington’s Efforts to Ensure AI Safety", "text": "NVIDIA Lends Support to Washington’s Efforts to Ensure AI Safety\r\nNVIDIA joins leaders from the White House, Congress and tech industry to discuss AI standards and best practices.\r\nSeptember 12, 2023 by <PERSON> testifies at Senate hearing on AI\r\n Share\r\n  \r\nIn an event at the White House today, NVIDIA announced support for voluntary commitments that the Biden Administration developed to ensure advanced AI systems are safe, secure and trustworthy.\r\n\r\nThe news came the same day NVIDIA’s chief scientist, <PERSON>, testified before a U.S. Senate subcommittee seeking input on potential legislation covering generative AI. Separately, NVIDIA founder and CEO <PERSON> will join other industry leaders in a closed-door meeting on AI Wednesday with the full Senate.\r\n\r\nSeven companies including Adobe, IBM, Palantir and Salesforce joined NVIDIA in supporting the eight agreements the <PERSON><PERSON><PERSON> administration released in July with support from Amazon, Anthropic, Google, Inflection, Meta, Microsoft and OpenAI.\r\n\r\nThe commitments are designed to advance common standards and best practices to ensure the safety of generative AI systems until regulations are in place, the White House said. They include:\r\n\r\nTesting the safety and capabilities of AI products before they’re deployed,\r\nSafeguarding AI models against cyber and insider threats, and\r\nUsing AI to help meet society’s greatest challenges, from cancer to climate change.\r\n<PERSON><PERSON> Shares NVIDIA’s Experience\r\nIn his testimony, <PERSON><PERSON> told the Senate subcommittee that government and industry should balance encouraging innovation in AI with ensuring models are deployed responsibly.\r\n\r\nThe subcommittee’s hearing, “Oversight of AI: Rules for Artificial Intelligence,” is among actions from policymakers around the world trying to identify and address potential risks of generative AI.\r\n\r\nEarlier this year, the subcommittee heard testimonies from leaders of Anthropic, IBM and OpenAI, as well as academics such as Yoshua Bengio, a University of Montreal professor considered one of the godfathers of AI.\r\n\r\nDally, who leads a global team of more than 300 at NVIDIA Research, shared the witness table on Tuesday with <PERSON> <PERSON>, Microsoft’s president and vice chair. Dally’s testimony briefly encapsulated NVIDIA’s unique role in the evolution of AI over the last two decades.\r\n\r\nHow Accelerated Computing Sparked AI\r\nHe described how NVIDIA invented the GPU in 1999 as a graphics processing unit, then fit it for a broader role in parallel processing in 2006 with the CUDA programming software. Over time, developers across diverse scientific and technical computing fields found this new form of accelerated computing could significantly advance their work.\r\n\r\nAlong the way, researchers discovered GPUs also were a natural fit for AI’s neural networks, because they require massive parallel processing.\r\n\r\nIn 2012, the AlexNet model, trained on two NVIDIA GPUs, demonstrated human-like capabilities in image recognition. That result helped spark a decade of rapid advances using GPUs, leading to ChatGPT and other generative AI models used by hundreds of millions worldwide.\r\n\r\nToday, accelerated computing and generative AI are showing the potential to transform industries, address global challenges and profoundly benefit society, said Dally, who chaired Stanford University’s computer science department before joining NVIDIA.\r\n\r\nAI’s Potential and Limits\r\nIn written testimony, Dally provided examples of how AI is empowering professionals to do their jobs better than they might have imagined in fields as diverse as business, healthcare and climate science.\r\n\r\nLike any technology, AI products and services have risks and are subject to existing laws and regulations that aim to mitigate those risks.\r\n\r\nIndustry also has a role to play in deploying AI responsibly. Developers set limits for AI models when they train them and define their outputs.\r\n\r\nDally noted that NVIDIA released in April NeMo Guardrails, open-source software developers can use to guide generative AI applications in producing accurate, appropriate and secure text responses. He said that NVIDIA also maintains internal risk-management guidelines for AI models.\r\n\r\nEyes on the Horizon\r\nMaking sure that new and exceptionally large AI models are accurate and safe is a natural role for regulators, Dally suggested.\r\n\r\nPicture of Sen Blumenthal welcoming Dally to the hearing\r\nSubcommittee chair Sen. Richard Blumenthal welcomed Dally to the hearing.\r\nHe said that these “frontier” models are being developed at a gigantic scale. They exceed the capabilities of ChatGPT and other existing models that have already been well-explored by developers and users.\r\n\r\nDally urged the subcommittee to balance thoughtful regulation with the need to encourage innovation in an AI developer community that includes thousands of startups, researchers and enterprises worldwide. AI tools should be widely available to ensure a level playing field, he said.\r\n\r\nDuring questioning, Senator Amy Klobuchar asked Dally why NVIDIA announced in March it’s working with Getty Images.\r\n\r\n“At NVIDIA, we believe in respecting people’s intellectual property rights,” Dally replied. “We partnered with Getty to train large language models with a service called Picasso, so people who provided the original content got remunerated.”\r\n\r\nIn closing, Dally reaffirmed NVIDIA’s dedication to innovating generative AI and accelerated computing in ways that serve the best interests of all.", "metadata": {"original_filename": "item041_US_NVIDIA Lends Support to Washington’s Efforts to Ensure AI Safety.txt", "upload_method": "batch_advanced_analysis", "model": "glm-4.5"}, "created_at": "2025-08-29T02:04:19.224789", "updated_at": "2025-08-29T02:04:19.224789", "word_count": 5511}