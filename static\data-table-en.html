<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="titles.tableView">Data Table - Document Analysis System</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <style>
        /* 数据表格页面特定样式 */
        .data-table-container {
            max-width: 1600px;
        }

        .table-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }

        .table-header p {
            margin: 0;
            font-size: 1.1em;
        }

        .controls-panel {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .data-tables {
            display: grid;
            gap: 30px;
        }

        .table-section {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .table-section h3 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
            font-size: 1.4em;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 10px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: var(--accent-color);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tr:hover {
            background: var(--bg-hover);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .table-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .document-selector {
            flex: 1;
            min-width: 200px;
        }

        .document-selector select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .loading {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .spinner {
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--accent-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1 data-i18n="titles.systemName">📄 Intelligent Document Analysis System</h1>
            <p class="subtitle" data-i18n="titles.subtitle">Advanced Policy Document Analysis Tool Powered by Zhipu AI</p>
            <nav class="main-nav">
                <a href="/" class="nav-link" data-i18n="nav.home">🏠 Document Analysis</a>
                <a href="/static/visualization-en.html" class="nav-link" data-i18n="nav.visualization">📊 Data Visualization</a>
                <a href="/static/data-table-en.html" class="nav-link active" data-i18n="nav.tableView">📋 Table View</a>
                <a href="/static/document-management-en.html" class="nav-link" data-i18n="nav.documentManagement">📁 Document Management</a>
                <a href="/static/prompts-en.html" class="nav-link" data-i18n="nav.promptEditor">🔧 Prompt Editor</a>
                <a href="/docs" class="nav-link" data-i18n="nav.apiDocs">📚 API Documentation</a>
                <button class="language-toggle" onclick="toggleLanguage()" title="Switch Language">中</button>
                <button class="theme-toggle" onclick="toggleTheme()" title="Switch Theme">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main class="data-table-container">
            <div class="table-header">
                <h1 data-i18n="table.title">📋 Data Table View</h1>
                <p data-i18n="table.description">View analysis results in structured table format for easy data review and export</p>
            </div>

            <!-- 控制面板 -->
            <div class="controls-panel">
                <div class="table-controls">
                    <div class="document-selector">
                        <label for="document-select" data-i18n="table.selectDocument">Select Document:</label>
                        <select id="document-select" onchange="loadTableData(this.value)">
                            <option value="" data-i18n="table.selectDocumentOption">-- Select a document --</option>
                        </select>
                    </div>
                    <button class="btn btn-secondary" onclick="exportTableData()" data-i18n="table.exportData">📥 Export Data</button>
                    <a href="/static/visualization-en.html" class="btn btn-info" data-i18n="table.returnToCharts">📈 Return to Charts</a>
                </div>
            </div>
        </main>

        <!-- 数据表格区域 -->
        <div class="data-tables" id="tables-container">
            <div class="loading">
                <div class="spinner"></div>
                <p data-i18n="table.selectDocumentPrompt">Please select a document and load data</p>
            </div>
        </div>
    </div>

    <script>
        let currentData = null;

        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon) {
                if (newTheme === 'dark') {
                    icon.textContent = '☀️';
                } else {
                    icon.textContent = '🌙';
                }
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Table page loaded');

            // 默认设置为英文
            switchLanguage('en');

            // 初始化主题
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon && savedTheme === 'dark') {
                icon.textContent = '☀️';
            }

            loadDocumentList();
        });

        // 加载文档列表
        async function loadDocumentList() {
            try {
                const response = await fetch('/api/v1/documents');
                if (response.ok) {
                    const documents = await response.json();
                    const select = document.getElementById('document-select');
                    
                    // 清空现有选项（保留默认选项）
                    select.innerHTML = `<option value="" data-i18n="table.selectDocumentOption">${t('table.selectDocumentOption')}</option>`;
                    
                    documents.forEach(doc => {
                        const option = document.createElement('option');
                        option.value = doc.id;
                        option.textContent = doc.title || doc.filename;
                        select.appendChild(option);
                    });
                } else {
                    console.error('Failed to load documents');
                }
            } catch (error) {
                console.error('Error loading documents:', error);
            }
        }

        // 加载表格数据
        async function loadTableData(docId) {
            if (!docId) {
                document.getElementById('tables-container').innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p data-i18n="table.selectDocumentPrompt">${t('table.selectDocumentPrompt')}</p>
                    </div>
                `;
                return;
            }

            const container = document.getElementById('tables-container');
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p data-i18n="common.loading">${t('common.loading')}</p>
                </div>
            `;

            try {
                const response = await fetch(`/api/v1/visualization/data/${docId}?lang=${currentLanguage}`);
                if (response.ok) {
                    currentData = await response.json();
                    renderTables(currentData);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('Failed to load data:', error);
                container.innerHTML = `
                    <div class="table-section">
                        <h3>❌ ${t('table.loadFailed')}</h3>
                        <p>${t('errors.dataLoadFailed')}: ${error.message}</p>
                    </div>
                `;
            }
        }

        function renderTables(data) {
            const container = document.getElementById('tables-container');
            let html = '';

            // 渲染各个数据表格
            if (data.policy_objectives && data.policy_objectives.length > 0) {
                html += createTableSection(t('table.policyObjectives'), data.policy_objectives);
            }

            if (data.implementation_measures && data.implementation_measures.length > 0) {
                html += createTableSection(t('table.implementationMeasures'), data.implementation_measures);
            }

            if (data.stakeholders && data.stakeholders.length > 0) {
                html += createTableSection(t('table.stakeholders'), data.stakeholders);
            }

            if (data.timeline && data.timeline.length > 0) {
                html += createTableSection(t('table.timeline'), data.timeline);
            }

            if (data.budget_allocation && data.budget_allocation.length > 0) {
                html += createTableSection(t('table.budgetAllocation'), data.budget_allocation);
            }

            if (data.risk_assessment && data.risk_assessment.length > 0) {
                html += createTableSection(t('table.riskAssessment'), data.risk_assessment);
            }

            if (data.performance_indicators && data.performance_indicators.length > 0) {
                html += createTableSection(t('table.performanceIndicators'), data.performance_indicators);
            }

            if (data.legal_compliance && data.legal_compliance.length > 0) {
                html += createTableSection(t('table.legalCompliance'), data.legal_compliance);
            }

            if (!html) {
                html = `
                    <div class="table-section">
                        <h3>📊 ${t('errors.noDataAvailable')}</h3>
                        <p>${t('table.noAnalysisData')}</p>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        function createTableSection(title, data) {
            if (!data || data.length === 0) return '';

            const headers = Object.keys(data[0]);
            
            let html = `
                <div class="table-section">
                    <h3>${title}</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                ${headers.map(header => `<th>${header}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${data.map(row => `
                                <tr>
                                    ${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            return html;
        }

        // 导出表格数据
        function exportTableData() {
            if (!currentData) {
                alert(t('errors.noDataAvailable'));
                return;
            }

            const dataStr = JSON.stringify(currentData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `analysis_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // 初始化国际化
        initI18n();
    </script>
</body>
</html>
