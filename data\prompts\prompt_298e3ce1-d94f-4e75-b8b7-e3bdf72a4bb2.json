{"id": "298e3ce1-d94f-4e75-b8b7-e3bdf72a4bb2", "task_type": "actor_relation", "name": "默认行为者关系分析", "description": "分析政策文档中的行为者与关系", "template": "请分析以下政策文档中的行为者与关系。\n\n文档内容：\n{document}\n\n请执行以下分析任务：\n1. 识别文档中的主要行为者（人物、组织、机构等等）\n2. 分析行为者之间的关系和互动\n3. 提取行为者的主要行动和立场\n4. 总结关键发现\n\n请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：\n{\n    \"actors\": [\n        {\n            \"name\": \"行为者名称\",\n            \"type\": \"行为者类型\",\n            \"description\": \"行为者描述\",\n            \"actions\": [\"行为者的主要行动\"],\n            \"stance\": \"行为者的立场或态度\"\n        }\n    ],\n    \"relations\": [\n        {\n            \"source\": \"行为者1名称\",\n            \"target\": \"行为者2名称\",\n            \"type\": \"关系类型\",\n            \"description\": \"关系描述\"\n        }\n    ],\n    \"key_findings\": [\"关键发现\"]\n}\n\n重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。", "version": "1.0.1", "is_active": true, "is_default": true, "created_at": "2025-08-28 14:02:00.946572", "updated_at": "2025-08-29 23:42:00.837999", "created_by": "user", "tags": ["默认", "行为者", "关系", "系统"], "performance_score": null}